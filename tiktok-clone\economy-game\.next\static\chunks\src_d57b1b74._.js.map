{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/lib/gameStore.ts"], "sourcesContent": ["'use client'\n\nimport { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// Types for game resources\nexport interface GameResources {\n  money: number\n  gems: number\n  energy: number\n  experience: number\n  level: number\n}\n\n// Types for bank account\nexport interface BankAccount {\n  balance: number\n  savingsBalance: number\n  loanAmount: number\n  interestRate: number\n  lastInterestUpdate: number\n}\n\n// Types for investments\nexport interface Investment {\n  id: string\n  name: string\n  type: 'stocks' | 'bonds' | 'crypto' | 'real_estate'\n  amount: number\n  currentValue: number\n  purchasePrice: number\n  purchaseDate: number\n  dailyReturn: number\n}\n\n// Types for jobs and income sources\nexport interface Job {\n  id: string\n  title: string\n  hourlyRate: number\n  energyCost: number\n  experienceGain: number\n  unlockLevel: number\n  isUnlocked: boolean\n}\n\n// Types for achievements\nexport interface Achievement {\n  id: string\n  title: string\n  description: string\n  reward: {\n    money?: number\n    gems?: number\n    experience?: number\n  }\n  isCompleted: boolean\n  progress: number\n  target: number\n}\n\n// Types for upgrades/items\nexport interface Upgrade {\n  id: string\n  name: string\n  description: string\n  cost: {\n    money?: number\n    gems?: number\n  }\n  effect: {\n    type: 'income_multiplier' | 'energy_regen' | 'interest_rate' | 'job_efficiency'\n    value: number\n  }\n  isPurchased: boolean\n  category: 'productivity' | 'banking' | 'energy' | 'special'\n}\n\n// Main game state interface\nexport interface GameState {\n  // Player resources\n  resources: GameResources\n  \n  // Banking system\n  bank: BankAccount\n  \n  // Investments\n  investments: Investment[]\n  \n  // Jobs and activities\n  availableJobs: Job[]\n  currentJob: string | null\n  \n  // Achievements\n  achievements: Achievement[]\n  \n  // Upgrades and items\n  upgrades: Upgrade[]\n  \n  // Game settings\n  lastSaveTime: number\n  offlineEarnings: number\n  \n  // Actions\n  addMoney: (amount: number) => void\n  spendMoney: (amount: number) => boolean\n  addGems: (amount: number) => void\n  spendGems: (amount: number) => boolean\n  addExperience: (amount: number) => void\n  useEnergy: (amount: number) => boolean\n  regenerateEnergy: () => void\n  \n  // Banking actions\n  depositMoney: (amount: number) => void\n  withdrawMoney: (amount: number) => boolean\n  takeLoan: (amount: number) => void\n  payLoan: (amount: number) => boolean\n  calculateInterest: () => void\n  \n  // Investment actions\n  buyInvestment: (investment: Omit<Investment, 'id' | 'currentValue' | 'purchaseDate'>) => void\n  sellInvestment: (investmentId: string) => void\n  updateInvestmentValues: () => void\n  \n  // Job actions\n  workJob: (jobId: string) => boolean\n  unlockJob: (jobId: string) => void\n  \n  // Achievement actions\n  checkAchievements: () => void\n  claimAchievement: (achievementId: string) => void\n  \n  // Upgrade actions\n  purchaseUpgrade: (upgradeId: string) => boolean\n  \n  // Game loop actions\n  calculateOfflineEarnings: () => void\n  saveGame: () => void\n  resetGame: () => void\n}\n\n// Default game state\nconst defaultGameState: Omit<GameState, keyof GameActions> = {\n  resources: {\n    money: 1000,\n    gems: 10,\n    energy: 100,\n    experience: 0,\n    level: 1\n  },\n  bank: {\n    balance: 0,\n    savingsBalance: 0,\n    loanAmount: 0,\n    interestRate: 0.05,\n    lastInterestUpdate: Date.now()\n  },\n  investments: [],\n  availableJobs: [\n    {\n      id: 'delivery',\n      title: 'توصيل الطلبات',\n      hourlyRate: 50,\n      energyCost: 10,\n      experienceGain: 5,\n      unlockLevel: 1,\n      isUnlocked: true\n    },\n    {\n      id: 'cashier',\n      title: 'كاشير',\n      hourlyRate: 75,\n      energyCost: 8,\n      experienceGain: 8,\n      unlockLevel: 3,\n      isUnlocked: false\n    },\n    {\n      id: 'programmer',\n      title: 'مبرمج',\n      hourlyRate: 200,\n      energyCost: 15,\n      experienceGain: 20,\n      unlockLevel: 10,\n      isUnlocked: false\n    }\n  ],\n  currentJob: null,\n  achievements: [\n    {\n      id: 'first_1000',\n      title: 'أول ألف',\n      description: 'اجمع 1000 دولار',\n      reward: { gems: 5 },\n      isCompleted: false,\n      progress: 0,\n      target: 1000\n    },\n    {\n      id: 'level_5',\n      title: 'المستوى الخامس',\n      description: 'وصل للمستوى 5',\n      reward: { money: 500, gems: 10 },\n      isCompleted: false,\n      progress: 0,\n      target: 5\n    }\n  ],\n  upgrades: [\n    {\n      id: 'energy_drink',\n      name: 'مشروب الطاقة',\n      description: 'يزيد سرعة استعادة الطاقة بنسبة 50%',\n      cost: { money: 500 },\n      effect: { type: 'energy_regen', value: 1.5 },\n      isPurchased: false,\n      category: 'energy'\n    },\n    {\n      id: 'productivity_boost',\n      name: 'تعزيز الإنتاجية',\n      description: 'يزيد الدخل من الوظائف بنسبة 25%',\n      cost: { money: 1000 },\n      effect: { type: 'income_multiplier', value: 1.25 },\n      isPurchased: false,\n      category: 'productivity'\n    }\n  ],\n  lastSaveTime: Date.now(),\n  offlineEarnings: 0\n}\n\ntype GameActions = Pick<GameState, \n  | 'addMoney' | 'spendMoney' | 'addGems' | 'spendGems' | 'addExperience' | 'useEnergy' | 'regenerateEnergy'\n  | 'depositMoney' | 'withdrawMoney' | 'takeLoan' | 'payLoan' | 'calculateInterest'\n  | 'buyInvestment' | 'sellInvestment' | 'updateInvestmentValues'\n  | 'workJob' | 'unlockJob'\n  | 'checkAchievements' | 'claimAchievement'\n  | 'purchaseUpgrade'\n  | 'calculateOfflineEarnings' | 'saveGame' | 'resetGame'\n>\n\n// Create the game store with persistence\nexport const useGameStore = create<GameState>()(\n  persist(\n    (set, get) => ({\n      ...defaultGameState,\n      \n      // Resource management\n      addMoney: (amount: number) => {\n        set((state) => ({\n          resources: {\n            ...state.resources,\n            money: state.resources.money + amount\n          }\n        }))\n        get().checkAchievements()\n      },\n      \n      spendMoney: (amount: number) => {\n        const state = get()\n        if (state.resources.money >= amount) {\n          set((prevState) => ({\n            resources: {\n              ...prevState.resources,\n              money: prevState.resources.money - amount\n            }\n          }))\n          return true\n        }\n        return false\n      },\n      \n      addGems: (amount: number) => {\n        set((state) => ({\n          resources: {\n            ...state.resources,\n            gems: state.resources.gems + amount\n          }\n        }))\n      },\n      \n      spendGems: (amount: number) => {\n        const state = get()\n        if (state.resources.gems >= amount) {\n          set((prevState) => ({\n            resources: {\n              ...prevState.resources,\n              gems: prevState.resources.gems - amount\n            }\n          }))\n          return true\n        }\n        return false\n      },\n      \n      addExperience: (amount: number) => {\n        set((state) => {\n          const newExp = state.resources.experience + amount\n          const newLevel = Math.floor(newExp / 100) + 1\n          \n          // Unlock jobs based on level\n          const updatedJobs = state.availableJobs.map(job => ({\n            ...job,\n            isUnlocked: job.unlockLevel <= newLevel\n          }))\n          \n          return {\n            resources: {\n              ...state.resources,\n              experience: newExp,\n              level: newLevel\n            },\n            availableJobs: updatedJobs\n          }\n        })\n        get().checkAchievements()\n      },\n      \n      useEnergy: (amount: number) => {\n        const state = get()\n        if (state.resources.energy >= amount) {\n          set((prevState) => ({\n            resources: {\n              ...prevState.resources,\n              energy: Math.max(0, prevState.resources.energy - amount)\n            }\n          }))\n          return true\n        }\n        return false\n      },\n      \n      regenerateEnergy: () => {\n        set((state) => {\n          const energyRegenRate = state.upgrades.find(u => u.id === 'energy_drink' && u.isPurchased) \n            ? 2 * 1.5 : 2 // Base regen is 2 per minute\n          \n          return {\n            resources: {\n              ...state.resources,\n              energy: Math.min(100, state.resources.energy + energyRegenRate)\n            }\n          }\n        })\n      },\n      \n      // Banking system\n      depositMoney: (amount: number) => {\n        if (get().spendMoney(amount)) {\n          set((state) => ({\n            bank: {\n              ...state.bank,\n              balance: state.bank.balance + amount\n            }\n          }))\n        }\n      },\n      \n      withdrawMoney: (amount: number) => {\n        const state = get()\n        if (state.bank.balance >= amount) {\n          set((prevState) => ({\n            bank: {\n              ...prevState.bank,\n              balance: prevState.bank.balance - amount\n            }\n          }))\n          get().addMoney(amount)\n          return true\n        }\n        return false\n      },\n      \n      takeLoan: (amount: number) => {\n        set((state) => ({\n          bank: {\n            ...state.bank,\n            loanAmount: state.bank.loanAmount + amount\n          }\n        }))\n        get().addMoney(amount)\n      },\n      \n      payLoan: (amount: number) => {\n        const state = get()\n        const payAmount = Math.min(amount, state.bank.loanAmount)\n        if (get().spendMoney(payAmount)) {\n          set((prevState) => ({\n            bank: {\n              ...prevState.bank,\n              loanAmount: prevState.bank.loanAmount - payAmount\n            }\n          }))\n          return true\n        }\n        return false\n      },\n      \n      calculateInterest: () => {\n        const state = get()\n        const now = Date.now()\n        const timeDiff = now - state.bank.lastInterestUpdate\n        const hoursPassed = timeDiff / (1000 * 60 * 60)\n        \n        if (hoursPassed >= 1) {\n          const savingsInterest = state.bank.savingsBalance * (state.bank.interestRate / 24) * hoursPassed\n          const loanInterest = state.bank.loanAmount * (state.bank.interestRate * 2 / 24) * hoursPassed\n          \n          set((prevState) => ({\n            bank: {\n              ...prevState.bank,\n              savingsBalance: prevState.bank.savingsBalance + savingsInterest,\n              loanAmount: prevState.bank.loanAmount + loanInterest,\n              lastInterestUpdate: now\n            }\n          }))\n        }\n      },\n      \n      // Investment system (placeholder for now)\n      buyInvestment: (investment) => {\n        // Implementation will be added later\n      },\n      \n      sellInvestment: (investmentId) => {\n        // Implementation will be added later\n      },\n      \n      updateInvestmentValues: () => {\n        // Implementation will be added later\n      },\n      \n      // Job system\n      workJob: (jobId: string) => {\n        const state = get()\n        const job = state.availableJobs.find(j => j.id === jobId)\n        \n        if (job && job.isUnlocked && get().useEnergy(job.energyCost)) {\n          const incomeMultiplier = state.upgrades.find(u => u.id === 'productivity_boost' && u.isPurchased) \n            ? 1.25 : 1\n          \n          const earnings = job.hourlyRate * incomeMultiplier\n          \n          get().addMoney(earnings)\n          get().addExperience(job.experienceGain)\n          \n          set({ currentJob: jobId })\n          \n          return true\n        }\n        return false\n      },\n      \n      unlockJob: (jobId: string) => {\n        set((state) => ({\n          availableJobs: state.availableJobs.map(job =>\n            job.id === jobId ? { ...job, isUnlocked: true } : job\n          )\n        }))\n      },\n      \n      // Achievement system\n      checkAchievements: () => {\n        const state = get()\n        \n        set((prevState) => ({\n          achievements: prevState.achievements.map(achievement => {\n            if (achievement.isCompleted) return achievement\n            \n            let progress = 0\n            \n            switch (achievement.id) {\n              case 'first_1000':\n                progress = state.resources.money\n                break\n              case 'level_5':\n                progress = state.resources.level\n                break\n            }\n            \n            const isCompleted = progress >= achievement.target\n            \n            return {\n              ...achievement,\n              progress: Math.min(progress, achievement.target),\n              isCompleted\n            }\n          })\n        }))\n      },\n      \n      claimAchievement: (achievementId: string) => {\n        const state = get()\n        const achievement = state.achievements.find(a => a.id === achievementId)\n        \n        if (achievement && achievement.isCompleted) {\n          if (achievement.reward.money) get().addMoney(achievement.reward.money)\n          if (achievement.reward.gems) get().addGems(achievement.reward.gems)\n          if (achievement.reward.experience) get().addExperience(achievement.reward.experience)\n        }\n      },\n      \n      // Upgrade system\n      purchaseUpgrade: (upgradeId: string) => {\n        const state = get()\n        const upgrade = state.upgrades.find(u => u.id === upgradeId)\n        \n        if (upgrade && !upgrade.isPurchased) {\n          let canAfford = true\n          \n          if (upgrade.cost.money && state.resources.money < upgrade.cost.money) canAfford = false\n          if (upgrade.cost.gems && state.resources.gems < upgrade.cost.gems) canAfford = false\n          \n          if (canAfford) {\n            if (upgrade.cost.money) get().spendMoney(upgrade.cost.money)\n            if (upgrade.cost.gems) get().spendGems(upgrade.cost.gems)\n            \n            set((prevState) => ({\n              upgrades: prevState.upgrades.map(u =>\n                u.id === upgradeId ? { ...u, isPurchased: true } : u\n              )\n            }))\n            \n            return true\n          }\n        }\n        return false\n      },\n      \n      // Game management\n      calculateOfflineEarnings: () => {\n        const state = get()\n        const now = Date.now()\n        const timeDiff = now - state.lastSaveTime\n        const hoursOffline = timeDiff / (1000 * 60 * 60)\n        \n        if (hoursOffline >= 0.1) { // At least 6 minutes offline\n          const offlineEarnings = Math.floor(hoursOffline * 10) // 10 coins per hour offline\n          \n          set({\n            offlineEarnings,\n            lastSaveTime: now\n          })\n        }\n      },\n      \n      saveGame: () => {\n        set({ lastSaveTime: Date.now() })\n      },\n      \n      resetGame: () => {\n        set(defaultGameState)\n      }\n    }),\n    {\n      name: 'economy-game-storage',\n      version: 1\n    }\n  )\n)\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;AA6IA,qBAAqB;AACrB,MAAM,mBAAuD;IAC3D,WAAW;QACT,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,OAAO;IACT;IACA,MAAM;QACJ,SAAS;QACT,gBAAgB;QAChB,YAAY;QACZ,cAAc;QACd,oBAAoB,KAAK,GAAG;IAC9B;IACA,aAAa,EAAE;IACf,eAAe;QACb;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,YAAY;QACd;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,YAAY;QACd;KACD;IACD,YAAY;IACZ,cAAc;QACZ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAE,MAAM;YAAE;YAClB,aAAa;YACb,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAE,OAAO;gBAAK,MAAM;YAAG;YAC/B,aAAa;YACb,UAAU;YACV,QAAQ;QACV;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;gBAAE,OAAO;YAAI;YACnB,QAAQ;gBAAE,MAAM;gBAAgB,OAAO;YAAI;YAC3C,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;gBAAE,OAAO;YAAK;YACpB,QAAQ;gBAAE,MAAM;gBAAqB,OAAO;YAAK;YACjD,aAAa;YACb,UAAU;QACZ;KACD;IACD,cAAc,KAAK,GAAG;IACtB,iBAAiB;AACnB;AAaO,MAAM,eAAe,SAC1B,QACE,CAAC,KAAK,MAAQ,CAAC;QACb,GAAG,gBAAgB;QAEnB,sBAAsB;QACtB,UAAU,CAAC;YACT,IAAI,CAAC,QAAU,CAAC;oBACd,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,OAAO,MAAM,SAAS,CAAC,KAAK,GAAG;oBACjC;gBACF,CAAC;YACD,MAAM,iBAAiB;QACzB;QAEA,YAAY,CAAC;YACX,MAAM,QAAQ;YACd,IAAI,MAAM,SAAS,CAAC,KAAK,IAAI,QAAQ;gBACnC,IAAI,CAAC,YAAc,CAAC;wBAClB,WAAW;4BACT,GAAG,UAAU,SAAS;4BACtB,OAAO,UAAU,SAAS,CAAC,KAAK,GAAG;wBACrC;oBACF,CAAC;gBACD,OAAO;YACT;YACA,OAAO;QACT;QAEA,SAAS,CAAC;YACR,IAAI,CAAC,QAAU,CAAC;oBACd,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,MAAM,MAAM,SAAS,CAAC,IAAI,GAAG;oBAC/B;gBACF,CAAC;QACH;QAEA,WAAW,CAAC;YACV,MAAM,QAAQ;YACd,IAAI,MAAM,SAAS,CAAC,IAAI,IAAI,QAAQ;gBAClC,IAAI,CAAC,YAAc,CAAC;wBAClB,WAAW;4BACT,GAAG,UAAU,SAAS;4BACtB,MAAM,UAAU,SAAS,CAAC,IAAI,GAAG;wBACnC;oBACF,CAAC;gBACD,OAAO;YACT;YACA,OAAO;QACT;QAEA,eAAe,CAAC;YACd,IAAI,CAAC;gBACH,MAAM,SAAS,MAAM,SAAS,CAAC,UAAU,GAAG;gBAC5C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,OAAO;gBAE5C,6BAA6B;gBAC7B,MAAM,cAAc,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAClD,GAAG,GAAG;wBACN,YAAY,IAAI,WAAW,IAAI;oBACjC,CAAC;gBAED,OAAO;oBACL,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,YAAY;wBACZ,OAAO;oBACT;oBACA,eAAe;gBACjB;YACF;YACA,MAAM,iBAAiB;QACzB;QAEA,WAAW,CAAC;YACV,MAAM,QAAQ;YACd,IAAI,MAAM,SAAS,CAAC,MAAM,IAAI,QAAQ;gBACpC,IAAI,CAAC,YAAc,CAAC;wBAClB,WAAW;4BACT,GAAG,UAAU,SAAS;4BACtB,QAAQ,KAAK,GAAG,CAAC,GAAG,UAAU,SAAS,CAAC,MAAM,GAAG;wBACnD;oBACF,CAAC;gBACD,OAAO;YACT;YACA,OAAO;QACT;QAEA,kBAAkB;YAChB,IAAI,CAAC;gBACH,MAAM,kBAAkB,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB,EAAE,WAAW,IACrF,IAAI,MAAM,EAAE,6BAA6B;;gBAE7C,OAAO;oBACL,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,QAAQ,KAAK,GAAG,CAAC,KAAK,MAAM,SAAS,CAAC,MAAM,GAAG;oBACjD;gBACF;YACF;QACF;QAEA,iBAAiB;QACjB,cAAc,CAAC;YACb,IAAI,MAAM,UAAU,CAAC,SAAS;gBAC5B,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb,SAAS,MAAM,IAAI,CAAC,OAAO,GAAG;wBAChC;oBACF,CAAC;YACH;QACF;QAEA,eAAe,CAAC;YACd,MAAM,QAAQ;YACd,IAAI,MAAM,IAAI,CAAC,OAAO,IAAI,QAAQ;gBAChC,IAAI,CAAC,YAAc,CAAC;wBAClB,MAAM;4BACJ,GAAG,UAAU,IAAI;4BACjB,SAAS,UAAU,IAAI,CAAC,OAAO,GAAG;wBACpC;oBACF,CAAC;gBACD,MAAM,QAAQ,CAAC;gBACf,OAAO;YACT;YACA,OAAO;QACT;QAEA,UAAU,CAAC;YACT,IAAI,CAAC,QAAU,CAAC;oBACd,MAAM;wBACJ,GAAG,MAAM,IAAI;wBACb,YAAY,MAAM,IAAI,CAAC,UAAU,GAAG;oBACtC;gBACF,CAAC;YACD,MAAM,QAAQ,CAAC;QACjB;QAEA,SAAS,CAAC;YACR,MAAM,QAAQ;YACd,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,UAAU;YACxD,IAAI,MAAM,UAAU,CAAC,YAAY;gBAC/B,IAAI,CAAC,YAAc,CAAC;wBAClB,MAAM;4BACJ,GAAG,UAAU,IAAI;4BACjB,YAAY,UAAU,IAAI,CAAC,UAAU,GAAG;wBAC1C;oBACF,CAAC;gBACD,OAAO;YACT;YACA,OAAO;QACT;QAEA,mBAAmB;YACjB,MAAM,QAAQ;YACd,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,kBAAkB;YACpD,MAAM,cAAc,WAAW,CAAC,OAAO,KAAK,EAAE;YAE9C,IAAI,eAAe,GAAG;gBACpB,MAAM,kBAAkB,MAAM,IAAI,CAAC,cAAc,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,EAAE,IAAI;gBACrF,MAAM,eAAe,MAAM,IAAI,CAAC,UAAU,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,IAAI;gBAElF,IAAI,CAAC,YAAc,CAAC;wBAClB,MAAM;4BACJ,GAAG,UAAU,IAAI;4BACjB,gBAAgB,UAAU,IAAI,CAAC,cAAc,GAAG;4BAChD,YAAY,UAAU,IAAI,CAAC,UAAU,GAAG;4BACxC,oBAAoB;wBACtB;oBACF,CAAC;YACH;QACF;QAEA,0CAA0C;QAC1C,eAAe,CAAC;QACd,qCAAqC;QACvC;QAEA,gBAAgB,CAAC;QACf,qCAAqC;QACvC;QAEA,wBAAwB;QACtB,qCAAqC;QACvC;QAEA,aAAa;QACb,OAAO,KAAE,CAAC;;YACR,MAAM,QAAQ;YACd,MAAM,MAAM,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAEnD,IAAI,OAAO,IAAI,UAAU,IAAI,MAAM,SAAS,CAAC,IAAI,UAAU,GAAG;gBAC5D,MAAM,mBAAmB,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,wBAAwB,EAAE,WAAW,IAC5F,OAAO;gBAEX,MAAM,WAAW,IAAI,UAAU,GAAG;gBAElC,MAAM,QAAQ,CAAC;gBACf,MAAM,aAAa,CAAC,IAAI,cAAc;gBAEtC,IAAI;oBAAE,YAAY;gBAAM;gBAExB,OAAO;YACT;YACA,OAAO;QACT;QAEA,WAAW,CAAC;YACV,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,MACrC,IAAI,EAAE,KAAK,QAAQ;4BAAE,GAAG,GAAG;4BAAE,YAAY;wBAAK,IAAI;gBAEtD,CAAC;QACH;QAEA,qBAAqB;QACrB,mBAAmB;YACjB,MAAM,QAAQ;YAEd,IAAI,CAAC,YAAc,CAAC;oBAClB,cAAc,UAAU,YAAY,CAAC,GAAG,CAAC,CAAA;wBACvC,IAAI,YAAY,WAAW,EAAE,OAAO;wBAEpC,IAAI,WAAW;wBAEf,OAAQ,YAAY,EAAE;4BACpB,KAAK;gCACH,WAAW,MAAM,SAAS,CAAC,KAAK;gCAChC;4BACF,KAAK;gCACH,WAAW,MAAM,SAAS,CAAC,KAAK;gCAChC;wBACJ;wBAEA,MAAM,cAAc,YAAY,YAAY,MAAM;wBAElD,OAAO;4BACL,GAAG,WAAW;4BACd,UAAU,KAAK,GAAG,CAAC,UAAU,YAAY,MAAM;4BAC/C;wBACF;oBACF;gBACF,CAAC;QACH;QAEA,kBAAkB,CAAC;YACjB,MAAM,QAAQ;YACd,MAAM,cAAc,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAE1D,IAAI,eAAe,YAAY,WAAW,EAAE;gBAC1C,IAAI,YAAY,MAAM,CAAC,KAAK,EAAE,MAAM,QAAQ,CAAC,YAAY,MAAM,CAAC,KAAK;gBACrE,IAAI,YAAY,MAAM,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,YAAY,MAAM,CAAC,IAAI;gBAClE,IAAI,YAAY,MAAM,CAAC,UAAU,EAAE,MAAM,aAAa,CAAC,YAAY,MAAM,CAAC,UAAU;YACtF;QACF;QAEA,iBAAiB;QACjB,iBAAiB,CAAC;YAChB,MAAM,QAAQ;YACd,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAElD,IAAI,WAAW,CAAC,QAAQ,WAAW,EAAE;gBACnC,IAAI,YAAY;gBAEhB,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,MAAM,SAAS,CAAC,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,YAAY;gBAClF,IAAI,QAAQ,IAAI,CAAC,IAAI,IAAI,MAAM,SAAS,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,YAAY;gBAE/E,IAAI,WAAW;oBACb,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE,MAAM,UAAU,CAAC,QAAQ,IAAI,CAAC,KAAK;oBAC3D,IAAI,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM,SAAS,CAAC,QAAQ,IAAI,CAAC,IAAI;oBAExD,IAAI,CAAC,YAAc,CAAC;4BAClB,UAAU,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,YAAY;oCAAE,GAAG,CAAC;oCAAE,aAAa;gCAAK,IAAI;wBAEvD,CAAC;oBAED,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAEA,kBAAkB;QAClB,0BAA0B;YACxB,MAAM,QAAQ;YACd,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,MAAM,MAAM,YAAY;YACzC,MAAM,eAAe,WAAW,CAAC,OAAO,KAAK,EAAE;YAE/C,IAAI,gBAAgB,KAAK;gBACvB,MAAM,kBAAkB,KAAK,KAAK,CAAC,eAAe,IAAI,4BAA4B;;gBAElF,IAAI;oBACF;oBACA,cAAc;gBAChB;YACF;QACF;QAEA,UAAU;YACR,IAAI;gBAAE,cAAc,KAAK,GAAG;YAAG;QACjC;QAEA,WAAW;YACT,IAAI;QACN;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS;AACX", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/ResourceDisplay.tsx"], "sourcesContent": ["'use client'\n\nimport { useGameStore } from '@/lib/gameStore'\nimport { DollarSign, Gem, Zap, Star } from 'lucide-react'\nimport { motion } from 'framer-motion'\n\nexport default function ResourceDisplay() {\n  const { resources } = useGameStore()\n\n  const formatNumber = (num: number) => {\n    if (num >= 1000000) {\n      return `${(num / 1000000).toFixed(1)}M`\n    } else if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}K`\n    }\n    return num.toLocaleString()\n  }\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n        {/* Money */}\n        <motion.div \n          className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-green-500 p-2 rounded-lg\">\n              <DollarSign className=\"w-4 h-4 text-white\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">المال</p>\n              <p className=\"font-bold text-lg text-green-400 truncate\">\n                ${formatNumber(resources.money)}\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Gems */}\n        <motion.div \n          className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-purple-500 p-2 rounded-lg\">\n              <Gem className=\"w-4 h-4 text-white\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">الجواهر</p>\n              <p className=\"font-bold text-lg text-purple-400 truncate\">\n                {formatNumber(resources.gems)}\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Energy */}\n        <motion.div \n          className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-blue-500 p-2 rounded-lg\">\n              <Zap className=\"w-4 h-4 text-white\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">الطاقة</p>\n              <p className=\"font-bold text-lg text-blue-400 truncate\">\n                {resources.energy}/100\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Level */}\n        <motion.div \n          className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-yellow-500 p-2 rounded-lg\">\n              <Star className=\"w-4 h-4 text-white\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">المستوى</p>\n              <p className=\"font-bold text-lg text-yellow-400 truncate\">\n                {resources.level}\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;AAFA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEjC,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS;YAClB,OAAO,AAAC,GAA6B,OAA3B,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,IAAG;QACvC,OAAO,IAAI,OAAO,MAAM;YACtB,OAAO,AAAC,GAA0B,OAAxB,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,IAAG;QACpC;QACA,OAAO,IAAI,cAAc;IAC3B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,OAAO,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;8BAExB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAW,WAAU;;;;;;;;;;;0CAExB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;;4CAA4C;4CACrD,aAAa,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAOtC,6LAAC,OAAO,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;8BAExB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;kDACV,aAAa,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;8BAOpC,6LAAC,OAAO,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;8BAExB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;;4CACV,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAO1B,6LAAC,OAAO,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;8BAExB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;kDACV,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GA7FwB;;QACA,0HAAA,CAAA,eAAY;;;KADZ", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/JobsPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useGameStore } from '@/lib/gameStore'\nimport { motion } from 'framer-motion'\nimport { Briefcase, DollarSign, Zap, Star, Lock } from 'lucide-react'\n\nexport default function JobsPanel() {\n  const { availableJobs, resources, workJob, currentJob } = useGameStore()\n\n  const handleWork = (jobId: string) => {\n    const success = workJob(jobId)\n    if (!success) {\n      // Could show a toast notification here\n      console.log('لا توجد طاقة كافية أو الوظيفة غير متاحة')\n    }\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h2 className=\"text-3xl font-bold mb-2\">الوظائف المتاحة</h2>\n        <p className=\"text-gray-300\">اختر وظيفة لكسب المال والخبرة</p>\n      </div>\n\n      {/* Energy warning */}\n      {resources.energy < 10 && (\n        <motion.div \n          className=\"bg-red-500/20 border border-red-500/30 rounded-lg p-4\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <p className=\"text-red-300 text-center\">\n            ⚠️ طاقتك منخفضة! انتظر قليلاً لتستعيد طاقتك أو اشتر مشروب طاقة من المتجر\n          </p>\n        </motion.div>\n      )}\n\n      {/* Jobs list */}\n      <div className=\"space-y-4\">\n        {availableJobs.map((job, index) => (\n          <motion.div\n            key={job.id}\n            className={`bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-200 ${\n              job.isUnlocked \n                ? 'border-white/20 hover:border-blue-500/50' \n                : 'border-gray-600/30'\n            } ${currentJob === job.id ? 'ring-2 ring-blue-500/50' : ''}`}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            whileHover={job.isUnlocked ? { scale: 1.02 } : {}}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <div className={`p-3 rounded-lg ${\n                  job.isUnlocked \n                    ? 'bg-blue-500' \n                    : 'bg-gray-600'\n                }`}>\n                  {job.isUnlocked ? (\n                    <Briefcase className=\"w-6 h-6 text-white\" />\n                  ) : (\n                    <Lock className=\"w-6 h-6 text-gray-400\" />\n                  )}\n                </div>\n                \n                <div className=\"flex-1\">\n                  <h3 className={`text-lg font-semibold ${\n                    job.isUnlocked ? 'text-white' : 'text-gray-400'\n                  }`}>\n                    {job.title}\n                  </h3>\n                  \n                  <div className=\"flex items-center space-x-4 mt-1\">\n                    <div className=\"flex items-center space-x-1\">\n                      <DollarSign className=\"w-4 h-4 text-green-400\" />\n                      <span className=\"text-sm text-green-400\">\n                        ${job.hourlyRate}/ساعة\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-1\">\n                      <Zap className=\"w-4 h-4 text-blue-400\" />\n                      <span className=\"text-sm text-blue-400\">\n                        -{job.energyCost}\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-1\">\n                      <Star className=\"w-4 h-4 text-yellow-400\" />\n                      <span className=\"text-sm text-yellow-400\">\n                        +{job.experienceGain} XP\n                      </span>\n                    </div>\n                  </div>\n                  \n                  {!job.isUnlocked && (\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      يتطلب المستوى {job.unlockLevel}\n                    </p>\n                  )}\n                </div>\n              </div>\n\n              {job.isUnlocked && (\n                <motion.button\n                  onClick={() => handleWork(job.id)}\n                  disabled={resources.energy < job.energyCost}\n                  className={`px-6 py-2 rounded-lg font-semibold transition-all duration-200 ${\n                    resources.energy >= job.energyCost\n                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 transform hover:scale-105'\n                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                  }`}\n                  whileHover={resources.energy >= job.energyCost ? { scale: 1.05 } : {}}\n                  whileTap={resources.energy >= job.energyCost ? { scale: 0.95 } : {}}\n                >\n                  {currentJob === job.id ? 'العمل الحالي' : 'ابدأ العمل'}\n                </motion.button>\n              )}\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Tips section */}\n      <motion.div \n        className=\"bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-xl p-4 border border-purple-500/30\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.5 }}\n      >\n        <h3 className=\"text-lg font-semibold mb-2\">💡 نصائح</h3>\n        <ul className=\"text-sm text-gray-300 space-y-1\">\n          <li>• الوظائف ذات الراتب الأعلى تتطلب طاقة أكثر</li>\n          <li>• اكسب الخبرة لفتح وظائف جديدة</li>\n          <li>• اشتر ترقيات من المتجر لزيادة كفاءتك</li>\n          <li>• الطاقة تتجدد تلقائياً كل دقيقة</li>\n        </ul>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;AAFA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAErE,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,QAAQ;QACxB,IAAI,CAAC,SAAS;YACZ,uCAAuC;YACvC,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAI9B,UAAU,MAAM,GAAG,oBAClB,6LAAC,OAAO,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;0BAE5B,cAAA,6LAAC;oBAAE,WAAU;8BAA2B;;;;;;;;;;;0BAO5C,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,KAAK,sBACvB,6LAAC,OAAO,GAAG;wBAET,WAAW,AAAC,kFAIR,OAHF,IAAI,UAAU,GACV,6CACA,sBACL,KAA0D,OAAvD,eAAe,IAAI,EAAE,GAAG,4BAA4B;wBACxD,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,YAAY,IAAI,UAAU,GAAG;4BAAE,OAAO;wBAAK,IAAI,CAAC;kCAEhD,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,kBAIhB,OAHC,IAAI,UAAU,GACV,gBACA;sDAEH,IAAI,UAAU,iBACb,6LAAC;gDAAU,WAAU;;;;;qEAErB,6LAAC;gDAAK,WAAU;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAW,AAAC,yBAEf,OADC,IAAI,UAAU,GAAG,eAAe;8DAE/B,IAAI,KAAK;;;;;;8DAGZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAW,WAAU;;;;;;8EACtB,6LAAC;oEAAK,WAAU;;wEAAyB;wEACrC,IAAI,UAAU;wEAAC;;;;;;;;;;;;;sEAIrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;;wEAAwB;wEACpC,IAAI,UAAU;;;;;;;;;;;;;sEAIpB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;;wEAA0B;wEACtC,IAAI,cAAc;wEAAC;;;;;;;;;;;;;;;;;;;gDAK1B,CAAC,IAAI,UAAU,kBACd,6LAAC;oDAAE,WAAU;;wDAA6B;wDACzB,IAAI,WAAW;;;;;;;;;;;;;;;;;;;gCAMrC,IAAI,UAAU,kBACb,6LAAC,OAAO,MAAM;oCACZ,SAAS,IAAM,WAAW,IAAI,EAAE;oCAChC,UAAU,UAAU,MAAM,GAAG,IAAI,UAAU;oCAC3C,WAAW,AAAC,kEAIX,OAHC,UAAU,MAAM,IAAI,IAAI,UAAU,GAC9B,8HACA;oCAEN,YAAY,UAAU,MAAM,IAAI,IAAI,UAAU,GAAG;wCAAE,OAAO;oCAAK,IAAI,CAAC;oCACpE,UAAU,UAAU,MAAM,IAAI,IAAI,UAAU,GAAG;wCAAE,OAAO;oCAAK,IAAI,CAAC;8CAEjE,eAAe,IAAI,EAAE,GAAG,iBAAiB;;;;;;;;;;;;uBA3E3C,IAAI,EAAE;;;;;;;;;;0BAoFjB,6LAAC,OAAO,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;GAxIwB;;QACoC,0HAAA,CAAA,eAAY;;;KADhD", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/BankPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useGameStore } from '@/lib/gameStore'\nimport { motion } from 'framer-motion'\nimport { Building, DollarSign, TrendingUp, CreditCard, ArrowUpRight, ArrowDownLeft } from 'lucide-react'\n\nexport default function BankPanel() {\n  const { \n    resources, \n    bank, \n    depositMoney, \n    withdrawMoney, \n    takeLoan, \n    payLoan \n  } = useGameStore()\n  \n  const [activeTab, setActiveTab] = useState<'account' | 'loan' | 'investment'>('account')\n  const [amount, setAmount] = useState('')\n\n  const handleDeposit = () => {\n    const value = parseInt(amount)\n    if (value > 0 && value <= resources.money) {\n      depositMoney(value)\n      setAmount('')\n    }\n  }\n\n  const handleWithdraw = () => {\n    const value = parseInt(amount)\n    if (value > 0 && value <= bank.balance) {\n      withdrawMoney(value)\n      setAmount('')\n    }\n  }\n\n  const handleTakeLoan = () => {\n    const value = parseInt(amount)\n    if (value > 0) {\n      takeLoan(value)\n      setAmount('')\n    }\n  }\n\n  const handlePayLoan = () => {\n    const value = parseInt(amount)\n    if (value > 0) {\n      payLoan(value)\n      setAmount('')\n    }\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h2 className=\"text-3xl font-bold mb-2\">🏦 البنك المركزي</h2>\n        <p className=\"text-gray-300\">إدارة أموالك واستثماراتك</p>\n      </div>\n\n      {/* Bank overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <motion.div \n          className=\"bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-md rounded-xl p-4 border border-green-500/30\"\n          whileHover={{ scale: 1.02 }}\n        >\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"bg-green-500 p-3 rounded-lg\">\n              <Building className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-300\">رصيد البنك</p>\n              <p className=\"text-xl font-bold text-green-400\">\n                ${bank.balance.toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div \n          className=\"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-md rounded-xl p-4 border border-blue-500/30\"\n          whileHover={{ scale: 1.02 }}\n        >\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"bg-blue-500 p-3 rounded-lg\">\n              <TrendingUp className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-300\">معدل الفائدة</p>\n              <p className=\"text-xl font-bold text-blue-400\">\n                {(bank.interestRate * 100).toFixed(1)}%\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div \n          className=\"bg-gradient-to-r from-red-500/20 to-pink-500/20 backdrop-blur-md rounded-xl p-4 border border-red-500/30\"\n          whileHover={{ scale: 1.02 }}\n        >\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"bg-red-500 p-3 rounded-lg\">\n              <CreditCard className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-300\">القروض</p>\n              <p className=\"text-xl font-bold text-red-400\">\n                ${bank.loanAmount.toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"flex space-x-1 bg-white/10 backdrop-blur-md rounded-lg p-1\">\n        {[\n          { id: 'account', label: 'الحساب', icon: Building },\n          { id: 'loan', label: 'القروض', icon: CreditCard },\n          { id: 'investment', label: 'الاستثمار', icon: TrendingUp }\n        ].map((tab) => {\n          const Icon = tab.icon\n          return (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id as any)}\n              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${\n                activeTab === tab.id\n                  ? 'bg-blue-500 text-white'\n                  : 'text-gray-300 hover:text-white hover:bg-white/10'\n              }`}\n            >\n              <Icon className=\"w-4 h-4\" />\n              <span className=\"font-medium\">{tab.label}</span>\n            </button>\n          )\n        })}\n      </div>\n\n      {/* Tab content */}\n      <div className=\"min-h-[300px]\">\n        {activeTab === 'account' && (\n          <motion.div \n            className=\"space-y-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\">\n              <h3 className=\"text-lg font-semibold mb-4\">إدارة الحساب</h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm text-gray-300 mb-2\">المبلغ</label>\n                  <input\n                    type=\"number\"\n                    value={amount}\n                    onChange={(e) => setAmount(e.target.value)}\n                    placeholder=\"أدخل المبلغ\"\n                    className=\"w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500\"\n                  />\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <motion.button\n                    onClick={handleDeposit}\n                    disabled={!amount || parseInt(amount) > resources.money}\n                    className=\"flex items-center justify-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-green-600 hover:to-emerald-700 transition-all duration-200\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <ArrowUpRight className=\"w-4 h-4\" />\n                    <span>إيداع</span>\n                  </motion.button>\n\n                  <motion.button\n                    onClick={handleWithdraw}\n                    disabled={!amount || parseInt(amount) > bank.balance}\n                    className=\"flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-500 to-cyan-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-600 hover:to-cyan-700 transition-all duration-200\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <ArrowDownLeft className=\"w-4 h-4\" />\n                    <span>سحب</span>\n                  </motion.button>\n                </div>\n\n                <div className=\"text-sm text-gray-400 space-y-1\">\n                  <p>• الحد الأدنى للإيداع: $10</p>\n                  <p>• فائدة سنوية: {(bank.interestRate * 100).toFixed(1)}%</p>\n                  <p>• الفائدة تُحسب كل ساعة</p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {activeTab === 'loan' && (\n          <motion.div \n            className=\"space-y-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\">\n              <h3 className=\"text-lg font-semibold mb-4\">إدارة القروض</h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm text-gray-300 mb-2\">مبلغ القرض</label>\n                  <input\n                    type=\"number\"\n                    value={amount}\n                    onChange={(e) => setAmount(e.target.value)}\n                    placeholder=\"أدخل المبلغ\"\n                    className=\"w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500\"\n                  />\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <motion.button\n                    onClick={handleTakeLoan}\n                    disabled={!amount}\n                    className=\"flex items-center justify-center space-x-2 bg-gradient-to-r from-orange-500 to-red-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-orange-600 hover:to-red-700 transition-all duration-200\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>اقتراض</span>\n                  </motion.button>\n\n                  <motion.button\n                    onClick={handlePayLoan}\n                    disabled={!amount || parseInt(amount) > resources.money || bank.loanAmount === 0}\n                    className=\"flex items-center justify-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-green-600 hover:to-emerald-700 transition-all duration-200\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>سداد</span>\n                  </motion.button>\n                </div>\n\n                <div className=\"text-sm text-gray-400 space-y-1\">\n                  <p>• فائدة القروض: {(bank.interestRate * 2 * 100).toFixed(1)}%</p>\n                  <p>• الحد الأقصى للقرض: $10,000</p>\n                  <p>• الفائدة تُحسب كل ساعة</p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {activeTab === 'investment' && (\n          <motion.div \n            className=\"space-y-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\">\n              <h3 className=\"text-lg font-semibold mb-4\">الاستثمارات</h3>\n              <p className=\"text-gray-400 text-center py-8\">\n                🚧 قريباً... نظام الاستثمار قيد التطوير\n              </p>\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;;;;;;;;;;AAHA;;;;;AAOe,SAAS;;IACtB,MAAM,EACJ,SAAS,EACT,IAAI,EACJ,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,OAAO,EACR,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,gBAAgB;QACpB,MAAM,QAAQ,SAAS;QACvB,IAAI,QAAQ,KAAK,SAAS,UAAU,KAAK,EAAE;YACzC,aAAa;YACb,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS;QACvB,IAAI,QAAQ,KAAK,SAAS,KAAK,OAAO,EAAE;YACtC,cAAc;YACd,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS;QACvB,IAAI,QAAQ,GAAG;YACb,SAAS;YACT,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,QAAQ,SAAS;QACvB,IAAI,QAAQ,GAAG;YACb,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,OAAO,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;kCAE1B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAS,WAAU;;;;;;;;;;;8CAEtB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAmC;gDAC5C,KAAK,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAMrC,6LAAC,OAAO,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;kCAE1B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAW,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDACV,CAAC,KAAK,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC,OAAO,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;kCAE1B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAW,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAiC;gDAC1C,KAAK,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,6LAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,IAAI;wBAAW,OAAO;wBAAU,MAAM;oBAAS;oBACjD;wBAAE,IAAI;wBAAQ,OAAO;wBAAU,MAAM;oBAAW;oBAChD;wBAAE,IAAI;wBAAc,OAAO;wBAAa,MAAM;oBAAW;iBAC1D,CAAC,GAAG,CAAC,CAAC;oBACL,MAAM,OAAO,IAAI,IAAI;oBACrB,qBACE,6LAAC;wBAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wBAClC,WAAW,AAAC,sGAIX,OAHC,cAAc,IAAI,EAAE,GAChB,2BACA;;0CAGN,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAe,IAAI,KAAK;;;;;;;uBATnC,IAAI,EAAE;;;;;gBAYjB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,2BACb,6LAAC,OAAO,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAE3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAmC;;;;;;8DACpD,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,OAAO,MAAM;oDACZ,SAAS;oDACT,UAAU,CAAC,UAAU,SAAS,UAAU,UAAU,KAAK;oDACvD,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;;sEAExB,6LAAC;4DAAa,WAAU;;;;;;sEACxB,6LAAC;sEAAK;;;;;;;;;;;;8DAGR,6LAAC,OAAO,MAAM;oDACZ,SAAS;oDACT,UAAU,CAAC,UAAU,SAAS,UAAU,KAAK,OAAO;oDACpD,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;;sEAExB,6LAAC;4DAAc,WAAU;;;;;;sEACzB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAIV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAE;;;;;;8DACH,6LAAC;;wDAAE;wDAAgB,CAAC,KAAK,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;8DACxD,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOZ,cAAc,wBACb,6LAAC,OAAO,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAE3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAmC;;;;;;8DACpD,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,OAAO,MAAM;oDACZ,SAAS;oDACT,UAAU,CAAC;oDACX,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,6LAAC;kEAAK;;;;;;;;;;;8DAGR,6LAAC,OAAO,MAAM;oDACZ,SAAS;oDACT,UAAU,CAAC,UAAU,SAAS,UAAU,UAAU,KAAK,IAAI,KAAK,UAAU,KAAK;oDAC/E,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAIV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;wDAAiB,CAAC,KAAK,YAAY,GAAG,IAAI,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;8DAC7D,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOZ,cAAc,8BACb,6LAAC,OAAO,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5D;GAnQwB;;QAQlB,0HAAA,CAAA,eAAY;;;KARM", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/ShopPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useGameStore } from '@/lib/gameStore'\nimport { motion } from 'framer-motion'\nimport { ShoppingBag, DollarSign, Gem, Zap, TrendingUp, Check } from 'lucide-react'\n\nexport default function ShopPanel() {\n  const { upgrades, resources, purchaseUpgrade } = useGameStore()\n\n  const handlePurchase = (upgradeId: string) => {\n    const success = purchaseUpgrade(upgradeId)\n    if (!success) {\n      console.log('لا يمكن شراء هذا العنصر')\n    }\n  }\n\n  const getUpgradeIcon = (category: string) => {\n    switch (category) {\n      case 'energy':\n        return Zap\n      case 'productivity':\n        return TrendingUp\n      case 'banking':\n        return DollarSign\n      default:\n        return ShoppingBag\n    }\n  }\n\n  const getUpgradeColor = (category: string) => {\n    switch (category) {\n      case 'energy':\n        return 'from-blue-500 to-cyan-600'\n      case 'productivity':\n        return 'from-green-500 to-emerald-600'\n      case 'banking':\n        return 'from-purple-500 to-pink-600'\n      default:\n        return 'from-gray-500 to-gray-600'\n    }\n  }\n\n  const canAfford = (upgrade: any) => {\n    if (upgrade.cost.money && resources.money < upgrade.cost.money) return false\n    if (upgrade.cost.gems && resources.gems < upgrade.cost.gems) return false\n    return true\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h2 className=\"text-3xl font-bold mb-2\">🛒 المتجر</h2>\n        <p className=\"text-gray-300\">اشتر الترقيات لتحسين أداءك</p>\n      </div>\n\n      {/* Categories */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n        <div className=\"bg-blue-500/20 backdrop-blur-md rounded-lg p-3 border border-blue-500/30 text-center\">\n          <Zap className=\"w-6 h-6 mx-auto mb-1 text-blue-400\" />\n          <p className=\"text-sm font-medium\">الطاقة</p>\n        </div>\n        <div className=\"bg-green-500/20 backdrop-blur-md rounded-lg p-3 border border-green-500/30 text-center\">\n          <TrendingUp className=\"w-6 h-6 mx-auto mb-1 text-green-400\" />\n          <p className=\"text-sm font-medium\">الإنتاجية</p>\n        </div>\n        <div className=\"bg-purple-500/20 backdrop-blur-md rounded-lg p-3 border border-purple-500/30 text-center\">\n          <DollarSign className=\"w-6 h-6 mx-auto mb-1 text-purple-400\" />\n          <p className=\"text-sm font-medium\">البنك</p>\n        </div>\n        <div className=\"bg-yellow-500/20 backdrop-blur-md rounded-lg p-3 border border-yellow-500/30 text-center\">\n          <ShoppingBag className=\"w-6 h-6 mx-auto mb-1 text-yellow-400\" />\n          <p className=\"text-sm font-medium\">خاص</p>\n        </div>\n      </div>\n\n      {/* Upgrades list */}\n      <div className=\"space-y-4\">\n        {upgrades.map((upgrade, index) => {\n          const Icon = getUpgradeIcon(upgrade.category)\n          const colorClass = getUpgradeColor(upgrade.category)\n          const affordable = canAfford(upgrade)\n\n          return (\n            <motion.div\n              key={upgrade.id}\n              className={`bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-200 ${\n                upgrade.isPurchased \n                  ? 'border-green-500/50 bg-green-500/10' \n                  : affordable \n                    ? 'border-white/20 hover:border-blue-500/50' \n                    : 'border-gray-600/30'\n              }`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              whileHover={!upgrade.isPurchased && affordable ? { scale: 1.02 } : {}}\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className={`p-3 rounded-lg bg-gradient-to-r ${colorClass}`}>\n                    {upgrade.isPurchased ? (\n                      <Check className=\"w-6 h-6 text-white\" />\n                    ) : (\n                      <Icon className=\"w-6 h-6 text-white\" />\n                    )}\n                  </div>\n                  \n                  <div className=\"flex-1\">\n                    <h3 className={`text-lg font-semibold ${\n                      upgrade.isPurchased ? 'text-green-400' : 'text-white'\n                    }`}>\n                      {upgrade.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-300 mb-2\">\n                      {upgrade.description}\n                    </p>\n                    \n                    <div className=\"flex items-center space-x-4\">\n                      {upgrade.cost.money && (\n                        <div className=\"flex items-center space-x-1\">\n                          <DollarSign className=\"w-4 h-4 text-green-400\" />\n                          <span className={`text-sm ${\n                            affordable ? 'text-green-400' : 'text-red-400'\n                          }`}>\n                            ${upgrade.cost.money.toLocaleString()}\n                          </span>\n                        </div>\n                      )}\n                      \n                      {upgrade.cost.gems && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Gem className=\"w-4 h-4 text-purple-400\" />\n                          <span className={`text-sm ${\n                            affordable ? 'text-purple-400' : 'text-red-400'\n                          }`}>\n                            {upgrade.cost.gems}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {!upgrade.isPurchased && (\n                  <motion.button\n                    onClick={() => handlePurchase(upgrade.id)}\n                    disabled={!affordable}\n                    className={`px-6 py-2 rounded-lg font-semibold transition-all duration-200 ${\n                      affordable\n                        ? `bg-gradient-to-r ${colorClass} text-white hover:opacity-90 transform hover:scale-105`\n                        : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                    }`}\n                    whileHover={affordable ? { scale: 1.05 } : {}}\n                    whileTap={affordable ? { scale: 0.95 } : {}}\n                  >\n                    شراء\n                  </motion.button>\n                )}\n\n                {upgrade.isPurchased && (\n                  <div className=\"px-6 py-2 bg-green-500/20 border border-green-500/30 rounded-lg\">\n                    <span className=\"text-green-400 font-semibold\">مُشترى</span>\n                  </div>\n                )}\n              </div>\n            </motion.div>\n          )\n        })}\n      </div>\n\n      {/* Tips section */}\n      <motion.div \n        className=\"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.5 }}\n      >\n        <h3 className=\"text-lg font-semibold mb-2\">💡 نصائح الشراء</h3>\n        <ul className=\"text-sm text-gray-300 space-y-1\">\n          <li>• ابدأ بترقيات الطاقة لتتمكن من العمل أكثر</li>\n          <li>• ترقيات الإنتاجية تزيد دخلك من الوظائف</li>\n          <li>• ترقيات البنك تحسن معدلات الفائدة</li>\n          <li>• بعض الترقيات تتطلب الجواهر النادرة</li>\n        </ul>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;AAFA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAE5D,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,gBAAgB;QAChC,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,UAAU,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE,OAAO;QACvE,IAAI,QAAQ,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO;QACpE,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAW,WAAU;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAW,WAAU;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAY,WAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oBACtB,MAAM,OAAO,eAAe,QAAQ,QAAQ;oBAC5C,MAAM,aAAa,gBAAgB,QAAQ,QAAQ;oBACnD,MAAM,aAAa,UAAU;oBAE7B,qBACE,6LAAC,OAAO,GAAG;wBAET,WAAW,AAAC,kFAMX,OALC,QAAQ,WAAW,GACf,wCACA,aACE,6CACA;wBAER,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,YAAY,CAAC,QAAQ,WAAW,IAAI,aAAa;4BAAE,OAAO;wBAAK,IAAI,CAAC;kCAEpE,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,mCAA6C,OAAX;sDAChD,QAAQ,WAAW,iBAClB,6LAAC;gDAAM,WAAU;;;;;qEAEjB,6LAAC;gDAAK,WAAU;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAW,AAAC,yBAEf,OADC,QAAQ,WAAW,GAAG,mBAAmB;8DAExC,QAAQ,IAAI;;;;;;8DAEf,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;wDACZ,QAAQ,IAAI,CAAC,KAAK,kBACjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAW,WAAU;;;;;;8EACtB,6LAAC;oEAAK,WAAW,AAAC,WAEjB,OADC,aAAa,mBAAmB;;wEAC9B;wEACA,QAAQ,IAAI,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;;wDAKxC,QAAQ,IAAI,CAAC,IAAI,kBAChB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAW,AAAC,WAEjB,OADC,aAAa,oBAAoB;8EAEhC,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQ7B,CAAC,QAAQ,WAAW,kBACnB,6LAAC,OAAO,MAAM;oCACZ,SAAS,IAAM,eAAe,QAAQ,EAAE;oCACxC,UAAU,CAAC;oCACX,WAAW,AAAC,kEAIX,OAHC,aACI,AAAC,oBAA8B,OAAX,YAAW,4DAC/B;oCAEN,YAAY,aAAa;wCAAE,OAAO;oCAAK,IAAI,CAAC;oCAC5C,UAAU,aAAa;wCAAE,OAAO;oCAAK,IAAI,CAAC;8CAC3C;;;;;;gCAKF,QAAQ,WAAW,kBAClB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;;uBA7EhD,QAAQ,EAAE;;;;;gBAmFrB;;;;;;0BAIF,6LAAC,OAAO,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;GAtLwB;;QAC2B,0HAAA,CAAA,eAAY;;;KADvC", "debugId": null}}, {"offset": {"line": 2290, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/AchievementsPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useGameStore } from '@/lib/gameStore'\nimport { motion } from 'framer-motion'\nimport { Trophy, Star, DollarSign, Gem, Check, Lock } from 'lucide-react'\n\nexport default function AchievementsPanel() {\n  const { achievements, claimAchievement } = useGameStore()\n\n  const handleClaim = (achievementId: string) => {\n    claimAchievement(achievementId)\n  }\n\n  const getProgressPercentage = (achievement: any) => {\n    return Math.min((achievement.progress / achievement.target) * 100, 100)\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h2 className=\"text-3xl font-bold mb-2\">🏆 الإنجازات</h2>\n        <p className=\"text-gray-300\">اكمل التحديات واحصل على المكافآت</p>\n      </div>\n\n      {/* Stats overview */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <div className=\"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-lg p-4 border border-yellow-500/30 text-center\">\n          <Trophy className=\"w-8 h-8 mx-auto mb-2 text-yellow-400\" />\n          <p className=\"text-2xl font-bold text-yellow-400\">\n            {achievements.filter(a => a.isCompleted).length}\n          </p>\n          <p className=\"text-sm text-gray-300\">مكتملة</p>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-md rounded-lg p-4 border border-blue-500/30 text-center\">\n          <Star className=\"w-8 h-8 mx-auto mb-2 text-blue-400\" />\n          <p className=\"text-2xl font-bold text-blue-400\">\n            {achievements.length}\n          </p>\n          <p className=\"text-sm text-gray-300\">المجموع</p>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-md rounded-lg p-4 border border-green-500/30 text-center\">\n          <DollarSign className=\"w-8 h-8 mx-auto mb-2 text-green-400\" />\n          <p className=\"text-2xl font-bold text-green-400\">\n            {achievements.reduce((total, a) => total + (a.reward.money || 0), 0)}\n          </p>\n          <p className=\"text-sm text-gray-300\">مكافآت مالية</p>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-lg p-4 border border-purple-500/30 text-center\">\n          <Gem className=\"w-8 h-8 mx-auto mb-2 text-purple-400\" />\n          <p className=\"text-2xl font-bold text-purple-400\">\n            {achievements.reduce((total, a) => total + (a.reward.gems || 0), 0)}\n          </p>\n          <p className=\"text-sm text-gray-300\">مكافآت جواهر</p>\n        </div>\n      </div>\n\n      {/* Achievements list */}\n      <div className=\"space-y-4\">\n        {achievements.map((achievement, index) => {\n          const progressPercentage = getProgressPercentage(achievement)\n          const isCompleted = achievement.isCompleted\n\n          return (\n            <motion.div\n              key={achievement.id}\n              className={`bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-200 ${\n                isCompleted \n                  ? 'border-yellow-500/50 bg-yellow-500/10' \n                  : 'border-white/20'\n              }`}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              whileHover={{ scale: 1.02 }}\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className={`p-3 rounded-lg ${\n                    isCompleted \n                      ? 'bg-gradient-to-r from-yellow-500 to-orange-500' \n                      : 'bg-gray-600'\n                  }`}>\n                    {isCompleted ? (\n                      <Trophy className=\"w-6 h-6 text-white\" />\n                    ) : (\n                      <Lock className=\"w-6 h-6 text-gray-400\" />\n                    )}\n                  </div>\n                  \n                  <div className=\"flex-1\">\n                    <h3 className={`text-lg font-semibold ${\n                      isCompleted ? 'text-yellow-400' : 'text-white'\n                    }`}>\n                      {achievement.title}\n                    </h3>\n                    <p className=\"text-sm text-gray-300 mb-2\">\n                      {achievement.description}\n                    </p>\n                    \n                    {/* Progress bar */}\n                    <div className=\"mb-2\">\n                      <div className=\"flex justify-between text-xs text-gray-400 mb-1\">\n                        <span>التقدم</span>\n                        <span>{achievement.progress.toLocaleString()} / {achievement.target.toLocaleString()}</span>\n                      </div>\n                      <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                        <motion.div \n                          className={`h-2 rounded-full ${\n                            isCompleted \n                              ? 'bg-gradient-to-r from-yellow-400 to-orange-500' \n                              : 'bg-gradient-to-r from-blue-400 to-purple-500'\n                          }`}\n                          initial={{ width: 0 }}\n                          animate={{ width: `${progressPercentage}%` }}\n                          transition={{ duration: 0.5, delay: index * 0.1 }}\n                        />\n                      </div>\n                    </div>\n                    \n                    {/* Rewards */}\n                    <div className=\"flex items-center space-x-4\">\n                      {achievement.reward.money && (\n                        <div className=\"flex items-center space-x-1\">\n                          <DollarSign className=\"w-4 h-4 text-green-400\" />\n                          <span className=\"text-sm text-green-400\">\n                            +${achievement.reward.money.toLocaleString()}\n                          </span>\n                        </div>\n                      )}\n                      \n                      {achievement.reward.gems && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Gem className=\"w-4 h-4 text-purple-400\" />\n                          <span className=\"text-sm text-purple-400\">\n                            +{achievement.reward.gems}\n                          </span>\n                        </div>\n                      )}\n\n                      {achievement.reward.experience && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Star className=\"w-4 h-4 text-yellow-400\" />\n                          <span className=\"text-sm text-yellow-400\">\n                            +{achievement.reward.experience} XP\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {isCompleted && (\n                  <motion.button\n                    onClick={() => handleClaim(achievement.id)}\n                    className=\"px-6 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    استلام\n                  </motion.button>\n                )}\n              </div>\n            </motion.div>\n          )\n        })}\n      </div>\n\n      {/* Daily challenges section */}\n      <motion.div \n        className=\"bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-xl p-4 border border-purple-500/30\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.5 }}\n      >\n        <h3 className=\"text-lg font-semibold mb-2\">🎯 التحديات اليومية</h3>\n        <p className=\"text-gray-300 text-center py-4\">\n          قريباً... تحديات يومية جديدة!\n        </p>\n      </motion.div>\n\n      {/* Tips section */}\n      <motion.div \n        className=\"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-md rounded-xl p-4 border border-blue-500/30\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.7 }}\n      >\n        <h3 className=\"text-lg font-semibold mb-2\">💡 نصائح الإنجازات</h3>\n        <ul className=\"text-sm text-gray-300 space-y-1\">\n          <li>• اكمل الإنجازات للحصول على مكافآت قيمة</li>\n          <li>• بعض الإنجازات تفتح محتوى جديد</li>\n          <li>• تحقق من التقدم بانتظام</li>\n          <li>• المكافآت تساعدك في تطوير شخصيتك</li>\n        </ul>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;AAFA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEtD,MAAM,cAAc,CAAC;QACnB,iBAAiB;IACnB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,KAAK,GAAG,CAAC,AAAC,YAAY,QAAQ,GAAG,YAAY,MAAM,GAAI,KAAK;IACrE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CACV,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;;;;;;0CAEjD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;gCAAE,WAAU;0CACV,aAAa,MAAM;;;;;;0CAEtB,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAW,WAAU;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,aAAa,MAAM,CAAC,CAAC,OAAO,IAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG;;;;;;0CAEpE,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CACV,aAAa,MAAM,CAAC,CAAC,OAAO,IAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG;;;;;;0CAEnE,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,aAAa;oBAC9B,MAAM,qBAAqB,sBAAsB;oBACjD,MAAM,cAAc,YAAY,WAAW;oBAE3C,qBACE,6LAAC,OAAO,GAAG;wBAET,WAAW,AAAC,kFAIX,OAHC,cACI,0CACA;wBAEN,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,YAAY;4BAAE,OAAO;wBAAK;kCAE1B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,kBAIhB,OAHC,cACI,mDACA;sDAEH,4BACC,6LAAC;gDAAO,WAAU;;;;;qEAElB,6LAAC;gDAAK,WAAU;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAW,AAAC,yBAEf,OADC,cAAc,oBAAoB;8DAEjC,YAAY,KAAK;;;;;;8DAEpB,6LAAC;oDAAE,WAAU;8DACV,YAAY,WAAW;;;;;;8DAI1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;;wEAAM,YAAY,QAAQ,CAAC,cAAc;wEAAG;wEAAI,YAAY,MAAM,CAAC,cAAc;;;;;;;;;;;;;sEAEpF,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,OAAO,GAAG;gEACT,WAAW,AAAC,oBAIX,OAHC,cACI,mDACA;gEAEN,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO,AAAC,GAAqB,OAAnB,oBAAmB;gEAAG;gEAC3C,YAAY;oEAAE,UAAU;oEAAK,OAAO,QAAQ;gEAAI;;;;;;;;;;;;;;;;;8DAMtD,6LAAC;oDAAI,WAAU;;wDACZ,YAAY,MAAM,CAAC,KAAK,kBACvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAW,WAAU;;;;;;8EACtB,6LAAC;oEAAK,WAAU;;wEAAyB;wEACpC,YAAY,MAAM,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;;wDAK/C,YAAY,MAAM,CAAC,IAAI,kBACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;;wEAA0B;wEACtC,YAAY,MAAM,CAAC,IAAI;;;;;;;;;;;;;wDAK9B,YAAY,MAAM,CAAC,UAAU,kBAC5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;;wEAA0B;wEACtC,YAAY,MAAM,CAAC,UAAU;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQ3C,6BACC,6LAAC,OAAO,MAAM;oCACZ,SAAS,IAAM,YAAY,YAAY,EAAE;oCACzC,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;;;;;;;uBA7FA,YAAY,EAAE;;;;;gBAoGzB;;;;;;0BAIF,6LAAC,OAAO,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAiC;;;;;;;;;;;;0BAMhD,6LAAC,OAAO,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;GAnMwB;;QACqB,0HAAA,CAAA,eAAY;;;KADjC", "debugId": null}}, {"offset": {"line": 2867, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/BottomNavigation.tsx"], "sourcesContent": ["'use client'\n\nimport { Home, Briefcase, Building, ShoppingBag, Trophy } from 'lucide-react'\nimport { motion } from 'framer-motion'\n\ntype ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'\n\ninterface BottomNavigationProps {\n  activePanel: ActivePanel\n  onPanelChange: (panel: ActivePanel) => void\n}\n\nexport default function BottomNavigation({ activePanel, onPanelChange }: BottomNavigationProps) {\n  const navItems = [\n    { id: 'home' as const, icon: Home, label: 'الرئيسية' },\n    { id: 'jobs' as const, icon: Briefcase, label: 'الوظائف' },\n    { id: 'bank' as const, icon: Building, label: 'البنك' },\n    { id: 'shop' as const, icon: ShoppingBag, label: 'المتجر' },\n    { id: 'achievements' as const, icon: Trophy, label: 'الإنجازات' },\n  ]\n\n  return (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-black/30 backdrop-blur-md border-t border-white/10\">\n      <div className=\"flex justify-around items-center py-2\">\n        {navItems.map((item) => {\n          const isActive = activePanel === item.id\n          const Icon = item.icon\n\n          return (\n            <motion.button\n              key={item.id}\n              onClick={() => onPanelChange(item.id)}\n              className={`flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 ${\n                isActive \n                  ? 'text-blue-400' \n                  : 'text-gray-400 hover:text-white'\n              }`}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <div className={`p-2 rounded-lg transition-all duration-200 ${\n                isActive \n                  ? 'bg-blue-500/20 border border-blue-500/30' \n                  : 'hover:bg-white/10'\n              }`}>\n                <Icon className=\"w-5 h-5\" />\n              </div>\n              <span className=\"text-xs mt-1 font-medium\">{item.label}</span>\n              \n              {isActive && (\n                <motion.div\n                  className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-400 rounded-full\"\n                  layoutId=\"activeIndicator\"\n                />\n              )}\n            </motion.button>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;AAYe,SAAS,iBAAiB,KAAqD;QAArD,EAAE,WAAW,EAAE,aAAa,EAAyB,GAArD;IACvC,MAAM,WAAW;QACf;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAW;QACrD;YAAE,IAAI;YAAiB,MAAM;YAAW,OAAO;QAAU;QACzD;YAAE,IAAI;YAAiB,MAAM;YAAU,OAAO;QAAQ;QACtD;YAAE,IAAI;YAAiB,MAAM;YAAa,OAAO;QAAS;QAC1D;YAAE,IAAI;YAAyB,MAAM;YAAQ,OAAO;QAAY;KACjE;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,WAAW,gBAAgB,KAAK,EAAE;gBACxC,MAAM,OAAO,KAAK,IAAI;gBAEtB,qBACE,6LAAC,OAAO,MAAM;oBAEZ,SAAS,IAAM,cAAc,KAAK,EAAE;oBACpC,WAAW,AAAC,wFAIX,OAHC,WACI,kBACA;oBAEN,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAK;;sCAExB,6LAAC;4BAAI,WAAW,AAAC,8CAIhB,OAHC,WACI,6CACA;sCAEJ,cAAA,6LAAC;gCAAK,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAK,WAAU;sCAA4B,KAAK,KAAK;;;;;;wBAErD,0BACC,6LAAC,OAAO,GAAG;4BACT,WAAU;4BACV,UAAS;;;;;;;mBAtBR,KAAK,EAAE;;;;;YA2BlB;;;;;;;;;;;AAIR;KAjDwB", "debugId": null}}, {"offset": {"line": 2991, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/GameDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useGameStore } from '@/lib/gameStore'\nimport ResourceDisplay from './ResourceDisplay'\nimport JobsPanel from './JobsPanel'\nimport BankPanel from './BankPanel'\nimport ShopPanel from './ShopPanel'\nimport AchievementsPanel from './AchievementsPanel'\nimport BottomNavigation from './BottomNavigation'\nimport { useState } from 'react'\n\ntype ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'\n\nexport default function GameDashboard() {\n  const [activePanel, setActivePanel] = useState<ActivePanel>('home')\n  const { \n    resources, \n    calculateOfflineEarnings, \n    offlineEarnings, \n    addMoney, \n    regenerateEnergy,\n    calculateInterest \n  } = useGameStore()\n\n  // Initialize game on mount\n  useEffect(() => {\n    calculateOfflineEarnings()\n    \n    // Set up energy regeneration interval (every minute)\n    const energyInterval = setInterval(() => {\n      regenerateEnergy()\n    }, 60000) // 1 minute\n\n    // Set up interest calculation interval (every hour)\n    const interestInterval = setInterval(() => {\n      calculateInterest()\n    }, 3600000) // 1 hour\n\n    return () => {\n      clearInterval(energyInterval)\n      clearInterval(interestInterval)\n    }\n  }, [calculateOfflineEarnings, regenerateEnergy, calculateInterest])\n\n  // Show offline earnings modal if there are any\n  useEffect(() => {\n    if (offlineEarnings > 0) {\n      // You could show a modal here\n      addMoney(offlineEarnings)\n    }\n  }, [offlineEarnings, addMoney])\n\n  const renderActivePanel = () => {\n    switch (activePanel) {\n      case 'jobs':\n        return <JobsPanel />\n      case 'bank':\n        return <BankPanel />\n      case 'shop':\n        return <ShopPanel />\n      case 'achievements':\n        return <AchievementsPanel />\n      default:\n        return <HomePanel />\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white\">\n      {/* Header with resources */}\n      <div className=\"sticky top-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10\">\n        <ResourceDisplay />\n      </div>\n\n      {/* Main content */}\n      <div className=\"pb-20\">\n        {renderActivePanel()}\n      </div>\n\n      {/* Bottom navigation */}\n      <BottomNavigation activePanel={activePanel} onPanelChange={setActivePanel} />\n    </div>\n  )\n}\n\n// Home panel component\nfunction HomePanel() {\n  const { resources, currentJob, availableJobs } = useGameStore()\n  \n  const currentJobInfo = availableJobs.find(job => job.id === currentJob)\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Welcome section */}\n      <div className=\"text-center py-8\">\n        <h1 className=\"text-4xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent\">\n          لعبة الاقتصاد\n        </h1>\n        <p className=\"text-lg text-gray-300\">\n          المستوى {resources.level} • {resources.experience} نقطة خبرة\n        </p>\n      </div>\n\n      {/* Quick stats */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20\">\n          <h3 className=\"text-sm text-gray-300 mb-1\">الطاقة</h3>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 bg-gray-700 rounded-full h-3\">\n              <div \n                className=\"bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300\"\n                style={{ width: `${resources.energy}%` }}\n              />\n            </div>\n            <span className=\"text-sm font-semibold\">{resources.energy}/100</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20\">\n          <h3 className=\"text-sm text-gray-300 mb-1\">التقدم للمستوى التالي</h3>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 bg-gray-700 rounded-full h-3\">\n              <div \n                className=\"bg-gradient-to-r from-purple-400 to-pink-500 h-3 rounded-full transition-all duration-300\"\n                style={{ width: `${(resources.experience % 100)}%` }}\n              />\n            </div>\n            <span className=\"text-sm font-semibold\">{resources.experience % 100}/100</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Current job status */}\n      {currentJobInfo && (\n        <div className=\"bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-md rounded-xl p-4 border border-green-500/30\">\n          <h3 className=\"text-lg font-semibold mb-2\">الوظيفة الحالية</h3>\n          <p className=\"text-gray-300\">{currentJobInfo.title}</p>\n          <p className=\"text-sm text-gray-400\">\n            الراتب: ${currentJobInfo.hourlyRate}/ساعة • الطاقة: {currentJobInfo.energyCost}\n          </p>\n        </div>\n      )}\n\n      {/* Quick actions */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <QuickActionCard\n          title=\"العمل السريع\"\n          description=\"اكسب المال بسرعة\"\n          icon=\"💼\"\n          color=\"from-green-500 to-emerald-600\"\n        />\n        <QuickActionCard\n          title=\"الاستثمار\"\n          description=\"نمي أموالك\"\n          icon=\"📈\"\n          color=\"from-blue-500 to-cyan-600\"\n        />\n      </div>\n\n      {/* Daily bonus section */}\n      <div className=\"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30\">\n        <h3 className=\"text-lg font-semibold mb-2\">🎁 المكافأة اليومية</h3>\n        <p className=\"text-gray-300 mb-3\">احصل على مكافأتك اليومية!</p>\n        <button className=\"w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold py-3 px-4 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105\">\n          استلام المكافأة\n        </button>\n      </div>\n    </div>\n  )\n}\n\n// Quick action card component\ninterface QuickActionCardProps {\n  title: string\n  description: string\n  icon: string\n  color: string\n}\n\nfunction QuickActionCard({ title, description, icon, color }: QuickActionCardProps) {\n  return (\n    <div className={`bg-gradient-to-r ${color} rounded-xl p-4 cursor-pointer transform hover:scale-105 transition-all duration-200 shadow-lg`}>\n      <div className=\"text-2xl mb-2\">{icon}</div>\n      <h3 className=\"font-semibold text-white\">{title}</h3>\n      <p className=\"text-sm text-white/80\">{description}</p>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,EACJ,SAAS,EACT,wBAAwB,EACxB,eAAe,EACf,QAAQ,EACR,gBAAgB,EAChB,iBAAiB,EAClB,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEf,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;YAEA,qDAAqD;YACrD,MAAM,iBAAiB;0DAAY;oBACjC;gBACF;yDAAG,OAAO,WAAW;;YAErB,oDAAoD;YACpD,MAAM,mBAAmB;4DAAY;oBACnC;gBACF;2DAAG,SAAS,SAAS;;YAErB;2CAAO;oBACL,cAAc;oBACd,cAAc;gBAChB;;QACF;kCAAG;QAAC;QAA0B;QAAkB;KAAkB;IAElE,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,kBAAkB,GAAG;gBACvB,8BAA8B;gBAC9B,SAAS;YACX;QACF;kCAAG;QAAC;QAAiB;KAAS;IAE9B,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,kIAAA,CAAA,UAAS;;;;;YACnB,KAAK;gBACH,qBAAO,6LAAC,kIAAA,CAAA,UAAS;;;;;YACnB,KAAK;gBACH,qBAAO,6LAAC,kIAAA,CAAA,UAAS;;;;;YACnB,KAAK;gBACH,qBAAO,6LAAC,0IAAA,CAAA,UAAiB;;;;;YAC3B;gBACE,qBAAO,6LAAC;;;;;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;;;;;;;;;;0BAIlB,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,6LAAC,yIAAA,CAAA,UAAgB;gBAAC,aAAa;gBAAa,eAAe;;;;;;;;;;;;AAGjE;GAtEwB;;QASlB,0HAAA,CAAA,eAAY;;;KATM;AAwExB,uBAAuB;AACvB,SAAS;;IACP,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAE5D,MAAM,iBAAiB,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IAE5D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuG;;;;;;kCAGrH,6LAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,UAAU,KAAK;4BAAC;4BAAI,UAAU,UAAU;4BAAC;;;;;;;;;;;;;0BAKtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAAmB,OAAjB,UAAU,MAAM,EAAC;4CAAG;;;;;;;;;;;kDAG3C,6LAAC;wCAAK,WAAU;;4CAAyB,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAI9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAA+B,OAA5B,UAAU,UAAU,GAAG,KAAK;4CAAG;;;;;;;;;;;kDAGvD,6LAAC;wCAAK,WAAU;;4CAAyB,UAAU,UAAU,GAAG;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;YAMzE,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAiB,eAAe,KAAK;;;;;;kCAClD,6LAAC;wBAAE,WAAU;;4BAAwB;4BACzB,eAAe,UAAU;4BAAC;4BAAiB,eAAe,UAAU;;;;;;;;;;;;;0BAMpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,OAAM;;;;;;kCAER,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,OAAM;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBAAO,WAAU;kCAAsM;;;;;;;;;;;;;;;;;;AAMhO;IAnFS;;QAC0C,0HAAA,CAAA,eAAY;;;MADtD;AA6FT,SAAS,gBAAgB,KAAyD;QAAzD,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAwB,GAAzD;IACvB,qBACE,6LAAC;QAAI,WAAW,AAAC,oBAAyB,OAAN,OAAM;;0BACxC,6LAAC;gBAAI,WAAU;0BAAiB;;;;;;0BAChC,6LAAC;gBAAG,WAAU;0BAA4B;;;;;;0BAC1C,6LAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C;MARS", "debugId": null}}]}
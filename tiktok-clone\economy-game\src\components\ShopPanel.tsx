'use client'

import { useGameStore } from '@/lib/gameStore'
import { motion } from 'framer-motion'
import { ShoppingBag, DollarSign, Gem, Zap, TrendingUp, Check } from 'lucide-react'

export default function ShopPanel() {
  const { upgrades, resources, purchaseUpgrade } = useGameStore()

  const handlePurchase = (upgradeId: string) => {
    const success = purchaseUpgrade(upgradeId)
    if (!success) {
      console.log('لا يمكن شراء هذا العنصر')
    }
  }

  const getUpgradeIcon = (category: string) => {
    switch (category) {
      case 'energy':
        return Zap
      case 'productivity':
        return TrendingUp
      case 'banking':
        return DollarSign
      default:
        return ShoppingBag
    }
  }

  const getUpgradeColor = (category: string) => {
    switch (category) {
      case 'energy':
        return 'from-blue-500 to-cyan-600'
      case 'productivity':
        return 'from-green-500 to-emerald-600'
      case 'banking':
        return 'from-purple-500 to-pink-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  const canAfford = (upgrade: any) => {
    if (upgrade.cost.money && resources.money < upgrade.cost.money) return false
    if (upgrade.cost.gems && resources.gems < upgrade.cost.gems) return false
    return true
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2">🛒 المتجر</h2>
        <p className="text-gray-300">اشتر الترقيات لتحسين أداءك</p>
      </div>

      {/* Categories */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <div className="bg-blue-500/20 backdrop-blur-md rounded-lg p-3 border border-blue-500/30 text-center">
          <Zap className="w-6 h-6 mx-auto mb-1 text-blue-400" />
          <p className="text-sm font-medium">الطاقة</p>
        </div>
        <div className="bg-green-500/20 backdrop-blur-md rounded-lg p-3 border border-green-500/30 text-center">
          <TrendingUp className="w-6 h-6 mx-auto mb-1 text-green-400" />
          <p className="text-sm font-medium">الإنتاجية</p>
        </div>
        <div className="bg-purple-500/20 backdrop-blur-md rounded-lg p-3 border border-purple-500/30 text-center">
          <DollarSign className="w-6 h-6 mx-auto mb-1 text-purple-400" />
          <p className="text-sm font-medium">البنك</p>
        </div>
        <div className="bg-yellow-500/20 backdrop-blur-md rounded-lg p-3 border border-yellow-500/30 text-center">
          <ShoppingBag className="w-6 h-6 mx-auto mb-1 text-yellow-400" />
          <p className="text-sm font-medium">خاص</p>
        </div>
      </div>

      {/* Upgrades list */}
      <div className="space-y-4">
        {upgrades.map((upgrade, index) => {
          const Icon = getUpgradeIcon(upgrade.category)
          const colorClass = getUpgradeColor(upgrade.category)
          const affordable = canAfford(upgrade)

          return (
            <motion.div
              key={upgrade.id}
              className={`bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-200 ${
                upgrade.isPurchased 
                  ? 'border-green-500/50 bg-green-500/10' 
                  : affordable 
                    ? 'border-white/20 hover:border-blue-500/50' 
                    : 'border-gray-600/30'
              }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={!upgrade.isPurchased && affordable ? { scale: 1.02 } : {}}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${colorClass}`}>
                    {upgrade.isPurchased ? (
                      <Check className="w-6 h-6 text-white" />
                    ) : (
                      <Icon className="w-6 h-6 text-white" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h3 className={`text-lg font-semibold ${
                      upgrade.isPurchased ? 'text-green-400' : 'text-white'
                    }`}>
                      {upgrade.name}
                    </h3>
                    <p className="text-sm text-gray-300 mb-2">
                      {upgrade.description}
                    </p>
                    
                    <div className="flex items-center space-x-4">
                      {upgrade.cost.money && (
                        <div className="flex items-center space-x-1">
                          <DollarSign className="w-4 h-4 text-green-400" />
                          <span className={`text-sm ${
                            affordable ? 'text-green-400' : 'text-red-400'
                          }`}>
                            ${upgrade.cost.money.toLocaleString()}
                          </span>
                        </div>
                      )}
                      
                      {upgrade.cost.gems && (
                        <div className="flex items-center space-x-1">
                          <Gem className="w-4 h-4 text-purple-400" />
                          <span className={`text-sm ${
                            affordable ? 'text-purple-400' : 'text-red-400'
                          }`}>
                            {upgrade.cost.gems}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {!upgrade.isPurchased && (
                  <motion.button
                    onClick={() => handlePurchase(upgrade.id)}
                    disabled={!affordable}
                    className={`px-6 py-2 rounded-lg font-semibold transition-all duration-200 ${
                      affordable
                        ? `bg-gradient-to-r ${colorClass} text-white hover:opacity-90 transform hover:scale-105`
                        : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    }`}
                    whileHover={affordable ? { scale: 1.05 } : {}}
                    whileTap={affordable ? { scale: 0.95 } : {}}
                  >
                    شراء
                  </motion.button>
                )}

                {upgrade.isPurchased && (
                  <div className="px-6 py-2 bg-green-500/20 border border-green-500/30 rounded-lg">
                    <span className="text-green-400 font-semibold">مُشترى</span>
                  </div>
                )}
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Tips section */}
      <motion.div 
        className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <h3 className="text-lg font-semibold mb-2">💡 نصائح الشراء</h3>
        <ul className="text-sm text-gray-300 space-y-1">
          <li>• ابدأ بترقيات الطاقة لتتمكن من العمل أكثر</li>
          <li>• ترقيات الإنتاجية تزيد دخلك من الوظائف</li>
          <li>• ترقيات البنك تحسن معدلات الفائدة</li>
          <li>• بعض الترقيات تتطلب الجواهر النادرة</li>
        </ul>
      </motion.div>
    </div>
  )
}

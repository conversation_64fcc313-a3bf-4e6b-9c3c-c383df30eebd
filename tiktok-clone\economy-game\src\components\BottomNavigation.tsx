'use client'

type ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'

interface BottomNavigationProps {
  activePanel: ActivePanel
  onPanelChange: (panel: ActivePanel) => void
}

export default function BottomNavigation({ activePanel, onPanelChange }: BottomNavigationProps) {
  const navItems = [
    { id: 'home' as const, icon: '🏠', label: 'الرئيسية' },
    { id: 'jobs' as const, icon: '💼', label: 'الوظائف' },
    { id: 'bank' as const, icon: '🏦', label: 'البنك' },
    { id: 'shop' as const, icon: '🛒', label: 'المتجر' },
    { id: 'achievements' as const, icon: '🏆', label: 'الإنجازات' },
  ]

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-black/30 backdrop-blur-md border-t border-white/10">
      <div className="flex justify-around items-center py-2">
        {navItems.map((item) => {
          const isActive = activePanel === item.id

          return (
            <button
              key={item.id}
              onClick={() => onPanelChange(item.id)}
              className={`flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 hover:scale-110 ${isActive
                  ? 'text-blue-400'
                  : 'text-gray-400 hover:text-white'
                }`}
            >
              <div className={`p-2 rounded-lg transition-all duration-200 ${isActive
                  ? 'bg-blue-500/20 border border-blue-500/30'
                  : 'hover:bg-white/10'
                }`}>
                <span className="text-lg">{item.icon}</span>
              </div>
              <span className="text-xs mt-1 font-medium">{item.label}</span>

              {isActive && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-400 rounded-full" />
              )}
            </button>
          )
        })}
      </div>
    </div>
  )
}

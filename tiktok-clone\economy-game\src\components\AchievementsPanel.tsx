'use client'

import { useGameStore } from '@/lib/gameStore'
import { motion } from 'framer-motion'
import { Trophy, Star, DollarSign, Gem, Check, Lock } from 'lucide-react'

export default function AchievementsPanel() {
  const { achievements, claimAchievement } = useGameStore()

  const handleClaim = (achievementId: string) => {
    claimAchievement(achievementId)
  }

  const getProgressPercentage = (achievement: any) => {
    return Math.min((achievement.progress / achievement.target) * 100, 100)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2">🏆 الإنجازات</h2>
        <p className="text-gray-300">اكمل التحديات واحصل على المكافآت</p>
      </div>

      {/* Stats overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-lg p-4 border border-yellow-500/30 text-center">
          <Trophy className="w-8 h-8 mx-auto mb-2 text-yellow-400" />
          <p className="text-2xl font-bold text-yellow-400">
            {achievements.filter(a => a.isCompleted).length}
          </p>
          <p className="text-sm text-gray-300">مكتملة</p>
        </div>

        <div className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-md rounded-lg p-4 border border-blue-500/30 text-center">
          <Star className="w-8 h-8 mx-auto mb-2 text-blue-400" />
          <p className="text-2xl font-bold text-blue-400">
            {achievements.length}
          </p>
          <p className="text-sm text-gray-300">المجموع</p>
        </div>

        <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-md rounded-lg p-4 border border-green-500/30 text-center">
          <DollarSign className="w-8 h-8 mx-auto mb-2 text-green-400" />
          <p className="text-2xl font-bold text-green-400">
            {achievements.reduce((total, a) => total + (a.reward.money || 0), 0)}
          </p>
          <p className="text-sm text-gray-300">مكافآت مالية</p>
        </div>

        <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-lg p-4 border border-purple-500/30 text-center">
          <Gem className="w-8 h-8 mx-auto mb-2 text-purple-400" />
          <p className="text-2xl font-bold text-purple-400">
            {achievements.reduce((total, a) => total + (a.reward.gems || 0), 0)}
          </p>
          <p className="text-sm text-gray-300">مكافآت جواهر</p>
        </div>
      </div>

      {/* Achievements list */}
      <div className="space-y-4">
        {achievements.map((achievement, index) => {
          const progressPercentage = getProgressPercentage(achievement)
          const isCompleted = achievement.isCompleted

          return (
            <motion.div
              key={achievement.id}
              className={`bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-200 ${
                isCompleted 
                  ? 'border-yellow-500/50 bg-yellow-500/10' 
                  : 'border-white/20'
              }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-lg ${
                    isCompleted 
                      ? 'bg-gradient-to-r from-yellow-500 to-orange-500' 
                      : 'bg-gray-600'
                  }`}>
                    {isCompleted ? (
                      <Trophy className="w-6 h-6 text-white" />
                    ) : (
                      <Lock className="w-6 h-6 text-gray-400" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h3 className={`text-lg font-semibold ${
                      isCompleted ? 'text-yellow-400' : 'text-white'
                    }`}>
                      {achievement.title}
                    </h3>
                    <p className="text-sm text-gray-300 mb-2">
                      {achievement.description}
                    </p>
                    
                    {/* Progress bar */}
                    <div className="mb-2">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>التقدم</span>
                        <span>{achievement.progress.toLocaleString()} / {achievement.target.toLocaleString()}</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div 
                          className={`h-2 rounded-full ${
                            isCompleted 
                              ? 'bg-gradient-to-r from-yellow-400 to-orange-500' 
                              : 'bg-gradient-to-r from-blue-400 to-purple-500'
                          }`}
                          initial={{ width: 0 }}
                          animate={{ width: `${progressPercentage}%` }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                        />
                      </div>
                    </div>
                    
                    {/* Rewards */}
                    <div className="flex items-center space-x-4">
                      {achievement.reward.money && (
                        <div className="flex items-center space-x-1">
                          <DollarSign className="w-4 h-4 text-green-400" />
                          <span className="text-sm text-green-400">
                            +${achievement.reward.money.toLocaleString()}
                          </span>
                        </div>
                      )}
                      
                      {achievement.reward.gems && (
                        <div className="flex items-center space-x-1">
                          <Gem className="w-4 h-4 text-purple-400" />
                          <span className="text-sm text-purple-400">
                            +{achievement.reward.gems}
                          </span>
                        </div>
                      )}

                      {achievement.reward.experience && (
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-400" />
                          <span className="text-sm text-yellow-400">
                            +{achievement.reward.experience} XP
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {isCompleted && (
                  <motion.button
                    onClick={() => handleClaim(achievement.id)}
                    className="px-6 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    استلام
                  </motion.button>
                )}
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Daily challenges section */}
      <motion.div 
        className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-xl p-4 border border-purple-500/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <h3 className="text-lg font-semibold mb-2">🎯 التحديات اليومية</h3>
        <p className="text-gray-300 text-center py-4">
          قريباً... تحديات يومية جديدة!
        </p>
      </motion.div>

      {/* Tips section */}
      <motion.div 
        className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-md rounded-xl p-4 border border-blue-500/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.7 }}
      >
        <h3 className="text-lg font-semibold mb-2">💡 نصائح الإنجازات</h3>
        <ul className="text-sm text-gray-300 space-y-1">
          <li>• اكمل الإنجازات للحصول على مكافآت قيمة</li>
          <li>• بعض الإنجازات تفتح محتوى جديد</li>
          <li>• تحقق من التقدم بانتظام</li>
          <li>• المكافآت تساعدك في تطوير شخصيتك</li>
        </ul>
      </motion.div>
    </div>
  )
}

'use client'

import { useEffect, useState } from 'react'
import { useGameStore } from '@/lib/gameStore'
import ResourceDisplay from './ResourceDisplay'
import BottomNavigation from './BottomNavigation'

type ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'

export default function GameDashboard() {
  const [activePanel, setActivePanel] = useState<ActivePanel>('home')
  const {
    resources,
    calculateOfflineEarnings,
    offlineEarnings,
    addMoney,
    regenerateEnergy,
    calculateInterest
  } = useGameStore()

  // Initialize game on mount
  useEffect(() => {
    calculateOfflineEarnings()

    // Set up energy regeneration interval (every minute)
    const energyInterval = setInterval(() => {
      regenerateEnergy()
    }, 60000) // 1 minute

    // Set up interest calculation interval (every hour)
    const interestInterval = setInterval(() => {
      calculateInterest()
    }, 3600000) // 1 hour

    return () => {
      clearInterval(energyInterval)
      clearInterval(interestInterval)
    }
  }, [calculateOfflineEarnings, regenerateEnergy, calculateInterest])

  // Show offline earnings modal if there are any
  useEffect(() => {
    if (offlineEarnings > 0) {
      // You could show a modal here
      addMoney(offlineEarnings)
    }
  }, [offlineEarnings, addMoney])

  const renderActivePanel = () => {
    switch (activePanel) {
      case 'jobs':
        return <div className="p-6 text-center"><h2 className="text-2xl">💼 الوظائف</h2><p className="text-gray-300 mt-4">قريباً...</p></div>
      case 'bank':
        return <div className="p-6 text-center"><h2 className="text-2xl">🏦 البنك</h2><p className="text-gray-300 mt-4">قريباً...</p></div>
      case 'shop':
        return <div className="p-6 text-center"><h2 className="text-2xl">🛒 المتجر</h2><p className="text-gray-300 mt-4">قريباً...</p></div>
      case 'achievements':
        return <div className="p-6 text-center"><h2 className="text-2xl">🏆 الإنجازات</h2><p className="text-gray-300 mt-4">قريباً...</p></div>
      default:
        return <HomePanel />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white">
      {/* Header with resources */}
      <div className="sticky top-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <ResourceDisplay />
      </div>

      {/* Main content */}
      <div className="pb-20">
        {renderActivePanel()}
      </div>

      {/* Bottom navigation */}
      <BottomNavigation activePanel={activePanel} onPanelChange={setActivePanel} />
    </div>
  )
}

// Home panel component
function HomePanel() {
  const { resources, currentJob, availableJobs } = useGameStore()

  const currentJobInfo = availableJobs.find(job => job.id === currentJob)

  return (
    <div className="p-6 space-y-6">
      {/* Welcome section */}
      <div className="text-center py-8">
        <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
          لعبة الاقتصاد
        </h1>
        <p className="text-lg text-gray-300">
          المستوى {resources.level} • {resources.experience} نقطة خبرة
        </p>
      </div>

      {/* Quick stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
          <h3 className="text-sm text-gray-300 mb-1">الطاقة</h3>
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-700 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${resources.energy}%` }}
              />
            </div>
            <span className="text-sm font-semibold">{resources.energy}/100</span>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
          <h3 className="text-sm text-gray-300 mb-1">التقدم للمستوى التالي</h3>
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-700 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-purple-400 to-pink-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${(resources.experience % 100)}%` }}
              />
            </div>
            <span className="text-sm font-semibold">{resources.experience % 100}/100</span>
          </div>
        </div>
      </div>

      {/* Current job status */}
      {currentJobInfo && (
        <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-md rounded-xl p-4 border border-green-500/30">
          <h3 className="text-lg font-semibold mb-2">الوظيفة الحالية</h3>
          <p className="text-gray-300">{currentJobInfo.title}</p>
          <p className="text-sm text-gray-400">
            الراتب: ${currentJobInfo.hourlyRate}/ساعة • الطاقة: {currentJobInfo.energyCost}
          </p>
        </div>
      )}

      {/* Quick actions */}
      <div className="grid grid-cols-2 gap-4">
        <QuickActionCard
          title="العمل السريع"
          description="اكسب المال بسرعة"
          icon="💼"
          color="from-green-500 to-emerald-600"
        />
        <QuickActionCard
          title="الاستثمار"
          description="نمي أموالك"
          icon="📈"
          color="from-blue-500 to-cyan-600"
        />
      </div>

      {/* Daily bonus section */}
      <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30">
        <h3 className="text-lg font-semibold mb-2">🎁 المكافأة اليومية</h3>
        <p className="text-gray-300 mb-3">احصل على مكافأتك اليومية!</p>
        <button className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold py-3 px-4 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105">
          استلام المكافأة
        </button>
      </div>
    </div>
  )
}

// Quick action card component
interface QuickActionCardProps {
  title: string
  description: string
  icon: string
  color: string
}

function QuickActionCard({ title, description, icon, color }: QuickActionCardProps) {
  return (
    <div className={`bg-gradient-to-r ${color} rounded-xl p-4 cursor-pointer transform hover:scale-105 transition-all duration-200 shadow-lg`}>
      <div className="text-2xl mb-2">{icon}</div>
      <h3 className="font-semibold text-white">{title}</h3>
      <p className="text-sm text-white/80">{description}</p>
    </div>
  )
}

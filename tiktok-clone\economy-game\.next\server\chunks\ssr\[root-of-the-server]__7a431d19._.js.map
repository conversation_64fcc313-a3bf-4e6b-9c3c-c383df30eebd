{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/lib/gameStore.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\n// Simple types for the game\nexport interface GameResources {\n  money: number\n  gems: number\n  energy: number\n  experience: number\n  level: number\n}\n\n// Simple game store without persistence for now\nlet gameState = {\n  resources: {\n    money: 1000,\n    gems: 10,\n    energy: 100,\n    experience: 0,\n    level: 1\n  },\n  bank: {\n    balance: 0,\n    savingsBalance: 0,\n    loanAmount: 0,\n    interestRate: 0.05,\n    lastInterestUpdate: Date.now()\n  },\n  investments: [],\n  availableJobs: [\n    {\n      id: 'delivery',\n      title: 'توصيل الطلبات',\n      hourlyRate: 50,\n      energyCost: 10,\n      experienceGain: 5,\n      unlockLevel: 1,\n      isUnlocked: true\n    }\n  ],\n  currentJob: null,\n  achievements: [],\n  upgrades: [],\n  lastSaveTime: Date.now(),\n  offlineEarnings: 0\n}\n\nexport const useGameStore = () => {\n  const [state, setState] = useState(gameState)\n\n  const updateState = (newState: any) => {\n    gameState = { ...gameState, ...newState }\n    setState(gameState)\n  }\n\n  return {\n    ...state,\n    addMoney: (amount: number) => {\n      updateState({\n        resources: {\n          ...state.resources,\n          money: state.resources.money + amount\n        }\n      })\n    },\n    spendMoney: (amount: number) => {\n      if (state.resources.money >= amount) {\n        updateState({\n          resources: {\n            ...state.resources,\n            money: state.resources.money - amount\n          }\n        })\n        return true\n      }\n      return false\n    },\n    addGems: (amount: number) => {\n      updateState({\n        resources: {\n          ...state.resources,\n          gems: state.resources.gems + amount\n        }\n      })\n    },\n    spendGems: (amount: number) => {\n      if (state.resources.gems >= amount) {\n        updateState({\n          resources: {\n            ...state.resources,\n            gems: state.resources.gems - amount\n          }\n        })\n        return true\n      }\n      return false\n    },\n    addExperience: (amount: number) => {\n      const newExp = state.resources.experience + amount\n      const newLevel = Math.floor(newExp / 100) + 1\n\n      updateState({\n        resources: {\n          ...state.resources,\n          experience: newExp,\n          level: newLevel\n        }\n      })\n    },\n    useEnergy: (amount: number) => {\n      if (state.resources.energy >= amount) {\n        updateState({\n          resources: {\n            ...state.resources,\n            energy: Math.max(0, state.resources.energy - amount)\n          }\n        })\n        return true\n      }\n      return false\n    },\n    regenerateEnergy: () => {\n      updateState({\n        resources: {\n          ...state.resources,\n          energy: Math.min(100, state.resources.energy + 2)\n        }\n      })\n    },\n    calculateOfflineEarnings: () => {},\n    calculateInterest: () => {},\n    workJob: (jobId: string) => false,\n    depositMoney: (amount: number) => {},\n    withdrawMoney: (amount: number) => false,\n    takeLoan: (amount: number) => {},\n    payLoan: (amount: number) => false,\n    buyInvestment: () => {},\n    sellInvestment: () => {},\n    updateInvestmentValues: () => {},\n    unlockJob: () => {},\n    checkAchievements: () => {},\n    claimAchievement: () => {},\n    purchaseUpgrade: () => false,\n    saveGame: () => {},\n    resetGame: () => {}\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAaA,gDAAgD;AAChD,IAAI,YAAY;IACd,WAAW;QACT,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,OAAO;IACT;IACA,MAAM;QACJ,SAAS;QACT,gBAAgB;QAChB,YAAY;QACZ,cAAc;QACd,oBAAoB,KAAK,GAAG;IAC9B;IACA,aAAa,EAAE;IACf,eAAe;QACb;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,YAAY;QACd;KACD;IACD,YAAY;IACZ,cAAc,EAAE;IAChB,UAAU,EAAE;IACZ,cAAc,KAAK,GAAG;IACtB,iBAAiB;AACnB;AAEO,MAAM,eAAe;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,cAAc,CAAC;QACnB,YAAY;YAAE,GAAG,SAAS;YAAE,GAAG,QAAQ;QAAC;QACxC,SAAS;IACX;IAEA,OAAO;QACL,GAAG,KAAK;QACR,UAAU,CAAC;YACT,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,OAAO,MAAM,SAAS,CAAC,KAAK,GAAG;gBACjC;YACF;QACF;QACA,YAAY,CAAC;YACX,IAAI,MAAM,SAAS,CAAC,KAAK,IAAI,QAAQ;gBACnC,YAAY;oBACV,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,OAAO,MAAM,SAAS,CAAC,KAAK,GAAG;oBACjC;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,SAAS,CAAC;YACR,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,MAAM,MAAM,SAAS,CAAC,IAAI,GAAG;gBAC/B;YACF;QACF;QACA,WAAW,CAAC;YACV,IAAI,MAAM,SAAS,CAAC,IAAI,IAAI,QAAQ;gBAClC,YAAY;oBACV,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,MAAM,MAAM,SAAS,CAAC,IAAI,GAAG;oBAC/B;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,eAAe,CAAC;YACd,MAAM,SAAS,MAAM,SAAS,CAAC,UAAU,GAAG;YAC5C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,OAAO;YAE5C,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,YAAY;oBACZ,OAAO;gBACT;YACF;QACF;QACA,WAAW,CAAC;YACV,IAAI,MAAM,SAAS,CAAC,MAAM,IAAI,QAAQ;gBACpC,YAAY;oBACV,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,SAAS,CAAC,MAAM,GAAG;oBAC/C;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,kBAAkB;YAChB,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,QAAQ,KAAK,GAAG,CAAC,KAAK,MAAM,SAAS,CAAC,MAAM,GAAG;gBACjD;YACF;QACF;QACA,0BAA0B,KAAO;QACjC,mBAAmB,KAAO;QAC1B,SAAS,CAAC,QAAkB;QAC5B,cAAc,CAAC,UAAoB;QACnC,eAAe,CAAC,SAAmB;QACnC,UAAU,CAAC,UAAoB;QAC/B,SAAS,CAAC,SAAmB;QAC7B,eAAe,KAAO;QACtB,gBAAgB,KAAO;QACvB,wBAAwB,KAAO;QAC/B,WAAW,KAAO;QAClB,mBAAmB,KAAO;QAC1B,kBAAkB,KAAO;QACzB,iBAAiB,IAAM;QACvB,UAAU,KAAO;QACjB,WAAW,KAAO;IACpB;AACF", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/ResourceDisplay.tsx"], "sourcesContent": ["'use client'\n\nimport { useGameStore } from '@/lib/gameStore'\n\nexport default function ResourceDisplay() {\n  const { resources } = useGameStore()\n\n  const formatNumber = (num: number) => {\n    if (num >= 1000000) {\n      return `${(num / 1000000).toFixed(1)}M`\n    } else if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}K`\n    }\n    return num.toLocaleString()\n  }\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n        {/* Money */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-green-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">💰</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">المال</p>\n              <p className=\"font-bold text-lg text-green-400 truncate\">\n                ${formatNumber(resources.money)}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Gems */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-purple-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">💎</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">الجواهر</p>\n              <p className=\"font-bold text-lg text-purple-400 truncate\">\n                {formatNumber(resources.gems)}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Energy */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-blue-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">⚡</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">الطاقة</p>\n              <p className=\"font-bold text-lg text-blue-400 truncate\">\n                {resources.energy}/100\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Level */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-yellow-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">⭐</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">المستوى</p>\n              <p className=\"font-bold text-lg text-yellow-400 truncate\">\n                {resources.level}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAEjC,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS;YAClB,OAAO,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,IAAI,OAAO,MAAM;YACtB,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC;QACA,OAAO,IAAI,cAAc;IAC3B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;;4CAA4C;4CACrD,aAAa,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAOtC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDACV,aAAa,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;8BAOpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;;4CACV,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAO1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDACV,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/BottomNavigation.tsx"], "sourcesContent": ["'use client'\n\ntype ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'\n\ninterface BottomNavigationProps {\n  activePanel: ActivePanel\n  onPanelChange: (panel: ActivePanel) => void\n}\n\nexport default function BottomNavigation({ activePanel, onPanelChange }: BottomNavigationProps) {\n  const navItems = [\n    { id: 'home' as const, icon: '🏠', label: 'الرئيسية' },\n    { id: 'jobs' as const, icon: '💼', label: 'الوظائف' },\n    { id: 'bank' as const, icon: '🏦', label: 'البنك' },\n    { id: 'shop' as const, icon: '🛒', label: 'المتجر' },\n    { id: 'achievements' as const, icon: '🏆', label: 'الإنجازات' },\n  ]\n\n  return (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-black/30 backdrop-blur-md border-t border-white/10\">\n      <div className=\"flex justify-around items-center py-2\">\n        {navItems.map((item) => {\n          const isActive = activePanel === item.id\n\n          return (\n            <button\n              key={item.id}\n              onClick={() => onPanelChange(item.id)}\n              className={`flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 hover:scale-110 ${isActive\n                  ? 'text-blue-400'\n                  : 'text-gray-400 hover:text-white'\n                }`}\n            >\n              <div className={`p-2 rounded-lg transition-all duration-200 ${isActive\n                  ? 'bg-blue-500/20 border border-blue-500/30'\n                  : 'hover:bg-white/10'\n                }`}>\n                <span className=\"text-lg\">{item.icon}</span>\n              </div>\n              <span className=\"text-xs mt-1 font-medium\">{item.label}</span>\n\n              {isActive && (\n                <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-400 rounded-full\" />\n              )}\n            </button>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AASe,SAAS,iBAAiB,EAAE,WAAW,EAAE,aAAa,EAAyB;IAC5F,MAAM,WAAW;QACf;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAW;QACrD;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAU;QACpD;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAQ;QAClD;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAS;QACnD;YAAE,IAAI;YAAyB,MAAM;YAAM,OAAO;QAAY;KAC/D;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,WAAW,gBAAgB,KAAK,EAAE;gBAExC,qBACE,8OAAC;oBAEC,SAAS,IAAM,cAAc,KAAK,EAAE;oBACpC,WAAW,CAAC,qGAAqG,EAAE,WAC7G,kBACA,kCACF;;sCAEJ,8OAAC;4BAAI,WAAW,CAAC,2CAA2C,EAAE,WACxD,6CACA,qBACF;sCACF,cAAA,8OAAC;gCAAK,WAAU;0CAAW,KAAK,IAAI;;;;;;;;;;;sCAEtC,8OAAC;4BAAK,WAAU;sCAA4B,KAAK,KAAK;;;;;;wBAErD,0BACC,8OAAC;4BAAI,WAAU;;;;;;;mBAhBZ,KAAK,EAAE;;;;;YAoBlB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/GameDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useGameStore } from '@/lib/gameStore'\nimport ResourceDisplay from './ResourceDisplay'\nimport BottomNavigation from './BottomNavigation'\n\ntype ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'\n\nexport default function GameDashboard() {\n  const [activePanel, setActivePanel] = useState<ActivePanel>('home')\n  const {\n    resources,\n    calculateOfflineEarnings,\n    offlineEarnings,\n    addMoney,\n    regenerateEnergy,\n    calculateInterest\n  } = useGameStore()\n\n  // Initialize game on mount\n  useEffect(() => {\n    calculateOfflineEarnings()\n\n    // Set up energy regeneration interval (every minute)\n    const energyInterval = setInterval(() => {\n      regenerateEnergy()\n    }, 60000) // 1 minute\n\n    // Set up interest calculation interval (every hour)\n    const interestInterval = setInterval(() => {\n      calculateInterest()\n    }, 3600000) // 1 hour\n\n    return () => {\n      clearInterval(energyInterval)\n      clearInterval(interestInterval)\n    }\n  }, [calculateOfflineEarnings, regenerateEnergy, calculateInterest])\n\n  // Show offline earnings modal if there are any\n  useEffect(() => {\n    if (offlineEarnings > 0) {\n      // You could show a modal here\n      addMoney(offlineEarnings)\n    }\n  }, [offlineEarnings, addMoney])\n\n  const renderActivePanel = () => {\n    switch (activePanel) {\n      case 'jobs':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">💼 الوظائف</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      case 'bank':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">🏦 البنك</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      case 'shop':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">🛒 المتجر</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      case 'achievements':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">🏆 الإنجازات</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      default:\n        return <HomePanel />\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white\">\n      {/* Header with resources */}\n      <div className=\"sticky top-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10\">\n        <ResourceDisplay />\n      </div>\n\n      {/* Main content */}\n      <div className=\"pb-20\">\n        {renderActivePanel()}\n      </div>\n\n      {/* Bottom navigation */}\n      <BottomNavigation activePanel={activePanel} onPanelChange={setActivePanel} />\n    </div>\n  )\n}\n\n// Home panel component\nfunction HomePanel() {\n  const { resources, currentJob, availableJobs } = useGameStore()\n\n  const currentJobInfo = availableJobs.find(job => job.id === currentJob)\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Welcome section */}\n      <div className=\"text-center py-8\">\n        <h1 className=\"text-4xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent\">\n          لعبة الاقتصاد\n        </h1>\n        <p className=\"text-lg text-gray-300\">\n          المستوى {resources.level} • {resources.experience} نقطة خبرة\n        </p>\n      </div>\n\n      {/* Quick stats */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20\">\n          <h3 className=\"text-sm text-gray-300 mb-1\">الطاقة</h3>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 bg-gray-700 rounded-full h-3\">\n              <div\n                className=\"bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300\"\n                style={{ width: `${resources.energy}%` }}\n              />\n            </div>\n            <span className=\"text-sm font-semibold\">{resources.energy}/100</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20\">\n          <h3 className=\"text-sm text-gray-300 mb-1\">التقدم للمستوى التالي</h3>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 bg-gray-700 rounded-full h-3\">\n              <div\n                className=\"bg-gradient-to-r from-purple-400 to-pink-500 h-3 rounded-full transition-all duration-300\"\n                style={{ width: `${(resources.experience % 100)}%` }}\n              />\n            </div>\n            <span className=\"text-sm font-semibold\">{resources.experience % 100}/100</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Current job status */}\n      {currentJobInfo && (\n        <div className=\"bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-md rounded-xl p-4 border border-green-500/30\">\n          <h3 className=\"text-lg font-semibold mb-2\">الوظيفة الحالية</h3>\n          <p className=\"text-gray-300\">{currentJobInfo.title}</p>\n          <p className=\"text-sm text-gray-400\">\n            الراتب: ${currentJobInfo.hourlyRate}/ساعة • الطاقة: {currentJobInfo.energyCost}\n          </p>\n        </div>\n      )}\n\n      {/* Quick actions */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <QuickActionCard\n          title=\"العمل السريع\"\n          description=\"اكسب المال بسرعة\"\n          icon=\"💼\"\n          color=\"from-green-500 to-emerald-600\"\n        />\n        <QuickActionCard\n          title=\"الاستثمار\"\n          description=\"نمي أموالك\"\n          icon=\"📈\"\n          color=\"from-blue-500 to-cyan-600\"\n        />\n      </div>\n\n      {/* Daily bonus section */}\n      <div className=\"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30\">\n        <h3 className=\"text-lg font-semibold mb-2\">🎁 المكافأة اليومية</h3>\n        <p className=\"text-gray-300 mb-3\">احصل على مكافأتك اليومية!</p>\n        <button className=\"w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold py-3 px-4 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105\">\n          استلام المكافأة\n        </button>\n      </div>\n    </div>\n  )\n}\n\n// Quick action card component\ninterface QuickActionCardProps {\n  title: string\n  description: string\n  icon: string\n  color: string\n}\n\nfunction QuickActionCard({ title, description, icon, color }: QuickActionCardProps) {\n  return (\n    <div className={`bg-gradient-to-r ${color} rounded-xl p-4 cursor-pointer transform hover:scale-105 transition-all duration-200 shadow-lg`}>\n      <div className=\"text-2xl mb-2\">{icon}</div>\n      <h3 className=\"font-semibold text-white\">{title}</h3>\n      <p className=\"text-sm text-white/80\">{description}</p>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,EACJ,SAAS,EACT,wBAAwB,EACxB,eAAe,EACf,QAAQ,EACR,gBAAgB,EAChB,iBAAiB,EAClB,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAEf,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,qDAAqD;QACrD,MAAM,iBAAiB,YAAY;YACjC;QACF,GAAG,OAAO,WAAW;;QAErB,oDAAoD;QACpD,MAAM,mBAAmB,YAAY;YACnC;QACF,GAAG,SAAS,SAAS;;QAErB,OAAO;YACL,cAAc;YACd,cAAc;QAChB;IACF,GAAG;QAAC;QAA0B;QAAkB;KAAkB;IAElE,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,GAAG;YACvB,8BAA8B;YAC9B,SAAS;QACX;IACF,GAAG;QAAC;QAAiB;KAAS;IAE9B,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;sCAAkB,8OAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAe,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YACpH,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;sCAAkB,8OAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAa,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YAClH,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;sCAAkB,8OAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAc,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YACnH,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;sCAAkB,8OAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAiB,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YACtH;gBACE,qBAAO,8OAAC;;;;;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;0BAIlB,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,8OAAC,sIAAA,CAAA,UAAgB;gBAAC,aAAa;gBAAa,eAAe;;;;;;;;;;;;AAGjE;AAEA,uBAAuB;AACvB,SAAS;IACP,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAE5D,MAAM,iBAAiB,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuG;;;;;;kCAGrH,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,UAAU,KAAK;4BAAC;4BAAI,UAAU,UAAU;4BAAC;;;;;;;;;;;;;0BAKtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,UAAU,MAAM,CAAC,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAG3C,8OAAC;wCAAK,WAAU;;4CAAyB,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAI9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAI,UAAU,UAAU,GAAG,IAAK,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAGvD,8OAAC;wCAAK,WAAU;;4CAAyB,UAAU,UAAU,GAAG;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;YAMzE,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAiB,eAAe,KAAK;;;;;;kCAClD,8OAAC;wBAAE,WAAU;;4BAAwB;4BACzB,eAAe,UAAU;4BAAC;4BAAiB,eAAe,UAAU;;;;;;;;;;;;;0BAMpF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,OAAM;;;;;;kCAER,8OAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,OAAM;;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC;wBAAO,WAAU;kCAAsM;;;;;;;;;;;;;;;;;;AAMhO;AAUA,SAAS,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAwB;IAChF,qBACE,8OAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,MAAM,8FAA8F,CAAC;;0BACvI,8OAAC;gBAAI,WAAU;0BAAiB;;;;;;0BAChC,8OAAC;gBAAG,WAAU;0BAA4B;;;;;;0BAC1C,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}
'use client'

import { useState } from 'react'
import { useGameStore } from '@/lib/gameStore'
import { motion } from 'framer-motion'
import { Building, DollarSign, TrendingUp, CreditCard, ArrowUpRight, ArrowDownLeft } from 'lucide-react'

export default function BankPanel() {
  const { 
    resources, 
    bank, 
    depositMoney, 
    withdrawMoney, 
    takeLoan, 
    payLoan 
  } = useGameStore()
  
  const [activeTab, setActiveTab] = useState<'account' | 'loan' | 'investment'>('account')
  const [amount, setAmount] = useState('')

  const handleDeposit = () => {
    const value = parseInt(amount)
    if (value > 0 && value <= resources.money) {
      depositMoney(value)
      setAmount('')
    }
  }

  const handleWithdraw = () => {
    const value = parseInt(amount)
    if (value > 0 && value <= bank.balance) {
      withdrawMoney(value)
      setAmount('')
    }
  }

  const handleTakeLoan = () => {
    const value = parseInt(amount)
    if (value > 0) {
      takeLoan(value)
      setAmount('')
    }
  }

  const handlePayLoan = () => {
    const value = parseInt(amount)
    if (value > 0) {
      payLoan(value)
      setAmount('')
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2">🏦 البنك المركزي</h2>
        <p className="text-gray-300">إدارة أموالك واستثماراتك</p>
      </div>

      {/* Bank overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div 
          className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-md rounded-xl p-4 border border-green-500/30"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center space-x-3">
            <div className="bg-green-500 p-3 rounded-lg">
              <Building className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-300">رصيد البنك</p>
              <p className="text-xl font-bold text-green-400">
                ${bank.balance.toLocaleString()}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div 
          className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-md rounded-xl p-4 border border-blue-500/30"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center space-x-3">
            <div className="bg-blue-500 p-3 rounded-lg">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-300">معدل الفائدة</p>
              <p className="text-xl font-bold text-blue-400">
                {(bank.interestRate * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div 
          className="bg-gradient-to-r from-red-500/20 to-pink-500/20 backdrop-blur-md rounded-xl p-4 border border-red-500/30"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center space-x-3">
            <div className="bg-red-500 p-3 rounded-lg">
              <CreditCard className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-300">القروض</p>
              <p className="text-xl font-bold text-red-400">
                ${bank.loanAmount.toLocaleString()}
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-white/10 backdrop-blur-md rounded-lg p-1">
        {[
          { id: 'account', label: 'الحساب', icon: Building },
          { id: 'loan', label: 'القروض', icon: CreditCard },
          { id: 'investment', label: 'الاستثمار', icon: TrendingUp }
        ].map((tab) => {
          const Icon = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="font-medium">{tab.label}</span>
            </button>
          )
        })}
      </div>

      {/* Tab content */}
      <div className="min-h-[300px]">
        {activeTab === 'account' && (
          <motion.div 
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold mb-4">إدارة الحساب</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-300 mb-2">المبلغ</label>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="أدخل المبلغ"
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <motion.button
                    onClick={handleDeposit}
                    disabled={!amount || parseInt(amount) > resources.money}
                    className="flex items-center justify-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-green-600 hover:to-emerald-700 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <ArrowUpRight className="w-4 h-4" />
                    <span>إيداع</span>
                  </motion.button>

                  <motion.button
                    onClick={handleWithdraw}
                    disabled={!amount || parseInt(amount) > bank.balance}
                    className="flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-500 to-cyan-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-600 hover:to-cyan-700 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <ArrowDownLeft className="w-4 h-4" />
                    <span>سحب</span>
                  </motion.button>
                </div>

                <div className="text-sm text-gray-400 space-y-1">
                  <p>• الحد الأدنى للإيداع: $10</p>
                  <p>• فائدة سنوية: {(bank.interestRate * 100).toFixed(1)}%</p>
                  <p>• الفائدة تُحسب كل ساعة</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'loan' && (
          <motion.div 
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold mb-4">إدارة القروض</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-300 mb-2">مبلغ القرض</label>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="أدخل المبلغ"
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-blue-500"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <motion.button
                    onClick={handleTakeLoan}
                    disabled={!amount}
                    className="flex items-center justify-center space-x-2 bg-gradient-to-r from-orange-500 to-red-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-orange-600 hover:to-red-700 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span>اقتراض</span>
                  </motion.button>

                  <motion.button
                    onClick={handlePayLoan}
                    disabled={!amount || parseInt(amount) > resources.money || bank.loanAmount === 0}
                    className="flex items-center justify-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:from-green-600 hover:to-emerald-700 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span>سداد</span>
                  </motion.button>
                </div>

                <div className="text-sm text-gray-400 space-y-1">
                  <p>• فائدة القروض: {(bank.interestRate * 2 * 100).toFixed(1)}%</p>
                  <p>• الحد الأقصى للقرض: $10,000</p>
                  <p>• الفائدة تُحسب كل ساعة</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'investment' && (
          <motion.div 
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold mb-4">الاستثمارات</h3>
              <p className="text-gray-400 text-center py-8">
                🚧 قريباً... نظام الاستثمار قيد التطوير
              </p>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/lib/gameStore.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\n// Simple types for the game\nexport interface GameResources {\n  money: number\n  gems: number\n  energy: number\n  experience: number\n  level: number\n}\n\n// Simple game store without persistence for now\nlet gameState = {\n  resources: {\n    money: 1000,\n    gems: 10,\n    energy: 100,\n    experience: 0,\n    level: 1\n  },\n  bank: {\n    balance: 0,\n    savingsBalance: 0,\n    loanAmount: 0,\n    interestRate: 0.05,\n    lastInterestUpdate: Date.now()\n  },\n  investments: [],\n  availableJobs: [\n    {\n      id: 'delivery',\n      title: 'توصيل الطلبات',\n      hourlyRate: 50,\n      energyCost: 10,\n      experienceGain: 5,\n      unlockLevel: 1,\n      isUnlocked: true\n    }\n  ],\n  currentJob: null,\n  achievements: [],\n  upgrades: [],\n  lastSaveTime: Date.now(),\n  offlineEarnings: 0\n}\n\nexport const useGameStore = () => {\n  const [state, setState] = useState(gameState)\n\n  const updateState = (newState: any) => {\n    gameState = { ...gameState, ...newState }\n    setState(gameState)\n  }\n\n  return {\n    ...state,\n    addMoney: (amount: number) => {\n      updateState({\n        resources: {\n          ...state.resources,\n          money: state.resources.money + amount\n        }\n      })\n    },\n    spendMoney: (amount: number) => {\n      if (state.resources.money >= amount) {\n        updateState({\n          resources: {\n            ...state.resources,\n            money: state.resources.money - amount\n          }\n        })\n        return true\n      }\n      return false\n    },\n    addGems: (amount: number) => {\n      updateState({\n        resources: {\n          ...state.resources,\n          gems: state.resources.gems + amount\n        }\n      })\n    },\n    spendGems: (amount: number) => {\n      if (state.resources.gems >= amount) {\n        updateState({\n          resources: {\n            ...state.resources,\n            gems: state.resources.gems - amount\n          }\n        })\n        return true\n      }\n      return false\n    },\n    addExperience: (amount: number) => {\n      const newExp = state.resources.experience + amount\n      const newLevel = Math.floor(newExp / 100) + 1\n\n      updateState({\n        resources: {\n          ...state.resources,\n          experience: newExp,\n          level: newLevel\n        }\n      })\n    },\n    useEnergy: (amount: number) => {\n      if (state.resources.energy >= amount) {\n        updateState({\n          resources: {\n            ...state.resources,\n            energy: Math.max(0, state.resources.energy - amount)\n          }\n        })\n        return true\n      }\n      return false\n    },\n    regenerateEnergy: () => {\n      updateState({\n        resources: {\n          ...state.resources,\n          energy: Math.min(100, state.resources.energy + 2)\n        }\n      })\n    },\n    calculateOfflineEarnings: () => {},\n    calculateInterest: () => {},\n    workJob: (jobId: string) => false,\n    depositMoney: (amount: number) => {},\n    withdrawMoney: (amount: number) => false,\n    takeLoan: (amount: number) => {},\n    payLoan: (amount: number) => false,\n    buyInvestment: () => {},\n    sellInvestment: () => {},\n    updateInvestmentValues: () => {},\n    unlockJob: () => {},\n    checkAchievements: () => {},\n    claimAchievement: () => {},\n    purchaseUpgrade: () => false,\n    saveGame: () => {},\n    resetGame: () => {}\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAaA,gDAAgD;AAChD,IAAI,YAAY;IACd,WAAW;QACT,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,OAAO;IACT;IACA,MAAM;QACJ,SAAS;QACT,gBAAgB;QAChB,YAAY;QACZ,cAAc;QACd,oBAAoB,KAAK,GAAG;IAC9B;IACA,aAAa,EAAE;IACf,eAAe;QACb;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,YAAY;QACd;KACD;IACD,YAAY;IACZ,cAAc,EAAE;IAChB,UAAU,EAAE;IACZ,cAAc,KAAK,GAAG;IACtB,iBAAiB;AACnB;AAEO,MAAM,eAAe;;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,cAAc,CAAC;QACnB,YAAY;YAAE,GAAG,SAAS;YAAE,GAAG,QAAQ;QAAC;QACxC,SAAS;IACX;IAEA,OAAO;QACL,GAAG,KAAK;QACR,UAAU,CAAC;YACT,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,OAAO,MAAM,SAAS,CAAC,KAAK,GAAG;gBACjC;YACF;QACF;QACA,YAAY,CAAC;YACX,IAAI,MAAM,SAAS,CAAC,KAAK,IAAI,QAAQ;gBACnC,YAAY;oBACV,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,OAAO,MAAM,SAAS,CAAC,KAAK,GAAG;oBACjC;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,SAAS,CAAC;YACR,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,MAAM,MAAM,SAAS,CAAC,IAAI,GAAG;gBAC/B;YACF;QACF;QACA,WAAW,CAAC;YACV,IAAI,MAAM,SAAS,CAAC,IAAI,IAAI,QAAQ;gBAClC,YAAY;oBACV,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,MAAM,MAAM,SAAS,CAAC,IAAI,GAAG;oBAC/B;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,eAAe,CAAC;YACd,MAAM,SAAS,MAAM,SAAS,CAAC,UAAU,GAAG;YAC5C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,OAAO;YAE5C,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,YAAY;oBACZ,OAAO;gBACT;YACF;QACF;QACA,WAAW,CAAC;YACV,IAAI,MAAM,SAAS,CAAC,MAAM,IAAI,QAAQ;gBACpC,YAAY;oBACV,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,SAAS,CAAC,MAAM,GAAG;oBAC/C;gBACF;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,kBAAkB;YAChB,YAAY;gBACV,WAAW;oBACT,GAAG,MAAM,SAAS;oBAClB,QAAQ,KAAK,GAAG,CAAC,KAAK,MAAM,SAAS,CAAC,MAAM,GAAG;gBACjD;YACF;QACF;QACA,0BAA0B,KAAO;QACjC,mBAAmB,KAAO;QAC1B,SAAS,CAAC,QAAkB;QAC5B,cAAc,CAAC,UAAoB;QACnC,eAAe,CAAC,SAAmB;QACnC,UAAU,CAAC,UAAoB;QAC/B,SAAS,CAAC,SAAmB;QAC7B,eAAe,KAAO;QACtB,gBAAgB,KAAO;QACvB,wBAAwB,KAAO;QAC/B,WAAW,KAAO;QAClB,mBAAmB,KAAO;QAC1B,kBAAkB,KAAO;QACzB,iBAAiB,IAAM;QACvB,UAAU,KAAO;QACjB,WAAW,KAAO;IACpB;AACF;GAnGa", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/ResourceDisplay.tsx"], "sourcesContent": ["'use client'\n\nimport { useGameStore } from '@/lib/gameStore'\n\nexport default function ResourceDisplay() {\n  const { resources } = useGameStore()\n\n  const formatNumber = (num: number) => {\n    if (num >= 1000000) {\n      return `${(num / 1000000).toFixed(1)}M`\n    } else if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}K`\n    }\n    return num.toLocaleString()\n  }\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n        {/* Money */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-green-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">💰</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">المال</p>\n              <p className=\"font-bold text-lg text-green-400 truncate\">\n                ${formatNumber(resources.money)}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Gems */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-purple-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">💎</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">الجواهر</p>\n              <p className=\"font-bold text-lg text-purple-400 truncate\">\n                {formatNumber(resources.gems)}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Energy */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-blue-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">⚡</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">الطاقة</p>\n              <p className=\"font-bold text-lg text-blue-400 truncate\">\n                {resources.energy}/100\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Level */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"bg-yellow-500 p-2 rounded-lg\">\n              <span className=\"text-white text-sm\">⭐</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-xs text-gray-300\">المستوى</p>\n              <p className=\"font-bold text-lg text-yellow-400 truncate\">\n                {resources.level}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEjC,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS;YAClB,OAAO,AAAC,GAA6B,OAA3B,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,IAAG;QACvC,OAAO,IAAI,OAAO,MAAM;YACtB,OAAO,AAAC,GAA0B,OAAxB,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,IAAG;QACpC;QACA,OAAO,IAAI,cAAc;IAC3B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;;4CAA4C;4CACrD,aAAa,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAOtC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;kDACV,aAAa,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;8BAOpC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;;4CACV,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAO1B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;0CAEvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;kDACV,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GA7EwB;;QACA,0HAAA,CAAA,eAAY;;;KADZ", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/BottomNavigation.tsx"], "sourcesContent": ["'use client'\n\ntype ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'\n\ninterface BottomNavigationProps {\n  activePanel: ActivePanel\n  onPanelChange: (panel: ActivePanel) => void\n}\n\nexport default function BottomNavigation({ activePanel, onPanelChange }: BottomNavigationProps) {\n  const navItems = [\n    { id: 'home' as const, icon: '🏠', label: 'الرئيسية' },\n    { id: 'jobs' as const, icon: '💼', label: 'الوظائف' },\n    { id: 'bank' as const, icon: '🏦', label: 'البنك' },\n    { id: 'shop' as const, icon: '🛒', label: 'المتجر' },\n    { id: 'achievements' as const, icon: '🏆', label: 'الإنجازات' },\n  ]\n\n  return (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-black/30 backdrop-blur-md border-t border-white/10\">\n      <div className=\"flex justify-around items-center py-2\">\n        {navItems.map((item) => {\n          const isActive = activePanel === item.id\n\n          return (\n            <button\n              key={item.id}\n              onClick={() => onPanelChange(item.id)}\n              className={`flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 hover:scale-110 ${isActive\n                  ? 'text-blue-400'\n                  : 'text-gray-400 hover:text-white'\n                }`}\n            >\n              <div className={`p-2 rounded-lg transition-all duration-200 ${isActive\n                  ? 'bg-blue-500/20 border border-blue-500/30'\n                  : 'hover:bg-white/10'\n                }`}>\n                <span className=\"text-lg\">{item.icon}</span>\n              </div>\n              <span className=\"text-xs mt-1 font-medium\">{item.label}</span>\n\n              {isActive && (\n                <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-400 rounded-full\" />\n              )}\n            </button>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AASe,SAAS,iBAAiB,KAAqD;QAArD,EAAE,WAAW,EAAE,aAAa,EAAyB,GAArD;IACvC,MAAM,WAAW;QACf;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAW;QACrD;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAU;QACpD;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAQ;QAClD;YAAE,IAAI;YAAiB,MAAM;YAAM,OAAO;QAAS;QACnD;YAAE,IAAI;YAAyB,MAAM;YAAM,OAAO;QAAY;KAC/D;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,WAAW,gBAAgB,KAAK,EAAE;gBAExC,qBACE,6LAAC;oBAEC,SAAS,IAAM,cAAc,KAAK,EAAE;oBACpC,WAAW,AAAC,wGAGT,OAHgH,WAC7G,kBACA;;sCAGN,6LAAC;4BAAI,WAAW,AAAC,8CAGd,OAH2D,WACxD,6CACA;sCAEJ,cAAA,6LAAC;gCAAK,WAAU;0CAAW,KAAK,IAAI;;;;;;;;;;;sCAEtC,6LAAC;4BAAK,WAAU;sCAA4B,KAAK,KAAK;;;;;;wBAErD,0BACC,6LAAC;4BAAI,WAAU;;;;;;;mBAhBZ,KAAK,EAAE;;;;;YAoBlB;;;;;;;;;;;AAIR;KAzCwB", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/src/components/GameDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useGameStore } from '@/lib/gameStore'\nimport ResourceDisplay from './ResourceDisplay'\nimport BottomNavigation from './BottomNavigation'\n\ntype ActivePanel = 'home' | 'jobs' | 'bank' | 'shop' | 'achievements'\n\nexport default function GameDashboard() {\n  const [activePanel, setActivePanel] = useState<ActivePanel>('home')\n  const {\n    resources,\n    calculateOfflineEarnings,\n    offlineEarnings,\n    addMoney,\n    regenerateEnergy,\n    calculateInterest\n  } = useGameStore()\n\n  // Initialize game on mount\n  useEffect(() => {\n    calculateOfflineEarnings()\n\n    // Set up energy regeneration interval (every minute)\n    const energyInterval = setInterval(() => {\n      regenerateEnergy()\n    }, 60000) // 1 minute\n\n    // Set up interest calculation interval (every hour)\n    const interestInterval = setInterval(() => {\n      calculateInterest()\n    }, 3600000) // 1 hour\n\n    return () => {\n      clearInterval(energyInterval)\n      clearInterval(interestInterval)\n    }\n  }, [calculateOfflineEarnings, regenerateEnergy, calculateInterest])\n\n  // Show offline earnings modal if there are any\n  useEffect(() => {\n    if (offlineEarnings > 0) {\n      // You could show a modal here\n      addMoney(offlineEarnings)\n    }\n  }, [offlineEarnings, addMoney])\n\n  const renderActivePanel = () => {\n    switch (activePanel) {\n      case 'jobs':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">💼 الوظائف</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      case 'bank':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">🏦 البنك</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      case 'shop':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">🛒 المتجر</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      case 'achievements':\n        return <div className=\"p-6 text-center\"><h2 className=\"text-2xl\">🏆 الإنجازات</h2><p className=\"text-gray-300 mt-4\">قريباً...</p></div>\n      default:\n        return <HomePanel />\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white\">\n      {/* Header with resources */}\n      <div className=\"sticky top-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10\">\n        <ResourceDisplay />\n      </div>\n\n      {/* Main content */}\n      <div className=\"pb-20\">\n        {renderActivePanel()}\n      </div>\n\n      {/* Bottom navigation */}\n      <BottomNavigation activePanel={activePanel} onPanelChange={setActivePanel} />\n    </div>\n  )\n}\n\n// Home panel component\nfunction HomePanel() {\n  const { resources, currentJob, availableJobs } = useGameStore()\n\n  const currentJobInfo = availableJobs.find(job => job.id === currentJob)\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Welcome section */}\n      <div className=\"text-center py-8\">\n        <h1 className=\"text-4xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent\">\n          لعبة الاقتصاد\n        </h1>\n        <p className=\"text-lg text-gray-300\">\n          المستوى {resources.level} • {resources.experience} نقطة خبرة\n        </p>\n      </div>\n\n      {/* Quick stats */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20\">\n          <h3 className=\"text-sm text-gray-300 mb-1\">الطاقة</h3>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 bg-gray-700 rounded-full h-3\">\n              <div\n                className=\"bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300\"\n                style={{ width: `${resources.energy}%` }}\n              />\n            </div>\n            <span className=\"text-sm font-semibold\">{resources.energy}/100</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20\">\n          <h3 className=\"text-sm text-gray-300 mb-1\">التقدم للمستوى التالي</h3>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 bg-gray-700 rounded-full h-3\">\n              <div\n                className=\"bg-gradient-to-r from-purple-400 to-pink-500 h-3 rounded-full transition-all duration-300\"\n                style={{ width: `${(resources.experience % 100)}%` }}\n              />\n            </div>\n            <span className=\"text-sm font-semibold\">{resources.experience % 100}/100</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Current job status */}\n      {currentJobInfo && (\n        <div className=\"bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-md rounded-xl p-4 border border-green-500/30\">\n          <h3 className=\"text-lg font-semibold mb-2\">الوظيفة الحالية</h3>\n          <p className=\"text-gray-300\">{currentJobInfo.title}</p>\n          <p className=\"text-sm text-gray-400\">\n            الراتب: ${currentJobInfo.hourlyRate}/ساعة • الطاقة: {currentJobInfo.energyCost}\n          </p>\n        </div>\n      )}\n\n      {/* Quick actions */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <QuickActionCard\n          title=\"العمل السريع\"\n          description=\"اكسب المال بسرعة\"\n          icon=\"💼\"\n          color=\"from-green-500 to-emerald-600\"\n        />\n        <QuickActionCard\n          title=\"الاستثمار\"\n          description=\"نمي أموالك\"\n          icon=\"📈\"\n          color=\"from-blue-500 to-cyan-600\"\n        />\n      </div>\n\n      {/* Daily bonus section */}\n      <div className=\"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30\">\n        <h3 className=\"text-lg font-semibold mb-2\">🎁 المكافأة اليومية</h3>\n        <p className=\"text-gray-300 mb-3\">احصل على مكافأتك اليومية!</p>\n        <button className=\"w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold py-3 px-4 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105\">\n          استلام المكافأة\n        </button>\n      </div>\n    </div>\n  )\n}\n\n// Quick action card component\ninterface QuickActionCardProps {\n  title: string\n  description: string\n  icon: string\n  color: string\n}\n\nfunction QuickActionCard({ title, description, icon, color }: QuickActionCardProps) {\n  return (\n    <div className={`bg-gradient-to-r ${color} rounded-xl p-4 cursor-pointer transform hover:scale-105 transition-all duration-200 shadow-lg`}>\n      <div className=\"text-2xl mb-2\">{icon}</div>\n      <h3 className=\"font-semibold text-white\">{title}</h3>\n      <p className=\"text-sm text-white/80\">{description}</p>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,EACJ,SAAS,EACT,wBAAwB,EACxB,eAAe,EACf,QAAQ,EACR,gBAAgB,EAChB,iBAAiB,EAClB,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEf,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;YAEA,qDAAqD;YACrD,MAAM,iBAAiB;0DAAY;oBACjC;gBACF;yDAAG,OAAO,WAAW;;YAErB,oDAAoD;YACpD,MAAM,mBAAmB;4DAAY;oBACnC;gBACF;2DAAG,SAAS,SAAS;;YAErB;2CAAO;oBACL,cAAc;oBACd,cAAc;gBAChB;;QACF;kCAAG;QAAC;QAA0B;QAAkB;KAAkB;IAElE,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,kBAAkB,GAAG;gBACvB,8BAA8B;gBAC9B,SAAS;YACX;QACF;kCAAG;QAAC;QAAiB;KAAS;IAE9B,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;sCAAkB,6LAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAe,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YACpH,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;sCAAkB,6LAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAa,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YAClH,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;sCAAkB,6LAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAc,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YACnH,KAAK;gBACH,qBAAO,6LAAC;oBAAI,WAAU;;sCAAkB,6LAAC;4BAAG,WAAU;sCAAW;;;;;;sCAAiB,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;YACtH;gBACE,qBAAO,6LAAC;;;;;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;;;;;;;;;;0BAIlB,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,6LAAC,yIAAA,CAAA,UAAgB;gBAAC,aAAa;gBAAa,eAAe;;;;;;;;;;;;AAGjE;GAtEwB;;QASlB,0HAAA,CAAA,eAAY;;;KATM;AAwExB,uBAAuB;AACvB,SAAS;;IACP,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAE5D,MAAM,iBAAiB,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IAE5D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuG;;;;;;kCAGrH,6LAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,UAAU,KAAK;4BAAC;4BAAI,UAAU,UAAU;4BAAC;;;;;;;;;;;;;0BAKtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAAmB,OAAjB,UAAU,MAAM,EAAC;4CAAG;;;;;;;;;;;kDAG3C,6LAAC;wCAAK,WAAU;;4CAAyB,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAI9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAA+B,OAA5B,UAAU,UAAU,GAAG,KAAK;4CAAG;;;;;;;;;;;kDAGvD,6LAAC;wCAAK,WAAU;;4CAAyB,UAAU,UAAU,GAAG;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;YAMzE,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAiB,eAAe,KAAK;;;;;;kCAClD,6LAAC;wBAAE,WAAU;;4BAAwB;4BACzB,eAAe,UAAU;4BAAC;4BAAiB,eAAe,UAAU;;;;;;;;;;;;;0BAMpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,OAAM;;;;;;kCAER,6LAAC;wBACC,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,OAAM;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBAAO,WAAU;kCAAsM;;;;;;;;;;;;;;;;;;AAMhO;IAnFS;;QAC0C,0HAAA,CAAA,eAAY;;;MADtD;AA6FT,SAAS,gBAAgB,KAAyD;QAAzD,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAwB,GAAzD;IACvB,qBACE,6LAAC;QAAI,WAAW,AAAC,oBAAyB,OAAN,OAAM;;0BACxC,6LAAC;gBAAI,WAAU;0BAAiB;;;;;;0BAChC,6LAAC;gBAAG,WAAU;0BAA4B;;;;;;0BAC1C,6LAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C;MARS", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/%D8%A8%D8%B1%D9%85%D8%AC%D9%8A%D8%A7%D8%AA/exl/12/tiktok-clone/economy-game/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}
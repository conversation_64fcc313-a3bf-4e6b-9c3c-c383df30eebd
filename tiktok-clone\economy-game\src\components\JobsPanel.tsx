'use client'

import { useGameStore } from '@/lib/gameStore'
import { motion } from 'framer-motion'
import { Briefcase, DollarSign, Zap, Star, Lock } from 'lucide-react'

export default function JobsPanel() {
  const { availableJobs, resources, workJob, currentJob } = useGameStore()

  const handleWork = (jobId: string) => {
    const success = workJob(jobId)
    if (!success) {
      // Could show a toast notification here
      console.log('لا توجد طاقة كافية أو الوظيفة غير متاحة')
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2">الوظائف المتاحة</h2>
        <p className="text-gray-300">اختر وظيفة لكسب المال والخبرة</p>
      </div>

      {/* Energy warning */}
      {resources.energy < 10 && (
        <motion.div 
          className="bg-red-500/20 border border-red-500/30 rounded-lg p-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <p className="text-red-300 text-center">
            ⚠️ طاقتك منخفضة! انتظر قليلاً لتستعيد طاقتك أو اشتر مشروب طاقة من المتجر
          </p>
        </motion.div>
      )}

      {/* Jobs list */}
      <div className="space-y-4">
        {availableJobs.map((job, index) => (
          <motion.div
            key={job.id}
            className={`bg-white/10 backdrop-blur-md rounded-xl p-4 border transition-all duration-200 ${
              job.isUnlocked 
                ? 'border-white/20 hover:border-blue-500/50' 
                : 'border-gray-600/30'
            } ${currentJob === job.id ? 'ring-2 ring-blue-500/50' : ''}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={job.isUnlocked ? { scale: 1.02 } : {}}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-lg ${
                  job.isUnlocked 
                    ? 'bg-blue-500' 
                    : 'bg-gray-600'
                }`}>
                  {job.isUnlocked ? (
                    <Briefcase className="w-6 h-6 text-white" />
                  ) : (
                    <Lock className="w-6 h-6 text-gray-400" />
                  )}
                </div>
                
                <div className="flex-1">
                  <h3 className={`text-lg font-semibold ${
                    job.isUnlocked ? 'text-white' : 'text-gray-400'
                  }`}>
                    {job.title}
                  </h3>
                  
                  <div className="flex items-center space-x-4 mt-1">
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4 text-green-400" />
                      <span className="text-sm text-green-400">
                        ${job.hourlyRate}/ساعة
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Zap className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-blue-400">
                        -{job.energyCost}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400" />
                      <span className="text-sm text-yellow-400">
                        +{job.experienceGain} XP
                      </span>
                    </div>
                  </div>
                  
                  {!job.isUnlocked && (
                    <p className="text-xs text-gray-500 mt-1">
                      يتطلب المستوى {job.unlockLevel}
                    </p>
                  )}
                </div>
              </div>

              {job.isUnlocked && (
                <motion.button
                  onClick={() => handleWork(job.id)}
                  disabled={resources.energy < job.energyCost}
                  className={`px-6 py-2 rounded-lg font-semibold transition-all duration-200 ${
                    resources.energy >= job.energyCost
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 transform hover:scale-105'
                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  }`}
                  whileHover={resources.energy >= job.energyCost ? { scale: 1.05 } : {}}
                  whileTap={resources.energy >= job.energyCost ? { scale: 0.95 } : {}}
                >
                  {currentJob === job.id ? 'العمل الحالي' : 'ابدأ العمل'}
                </motion.button>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Tips section */}
      <motion.div 
        className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-xl p-4 border border-purple-500/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <h3 className="text-lg font-semibold mb-2">💡 نصائح</h3>
        <ul className="text-sm text-gray-300 space-y-1">
          <li>• الوظائف ذات الراتب الأعلى تتطلب طاقة أكثر</li>
          <li>• اكسب الخبرة لفتح وظائف جديدة</li>
          <li>• اشتر ترقيات من المتجر لزيادة كفاءتك</li>
          <li>• الطاقة تتجدد تلقائياً كل دقيقة</li>
        </ul>
      </motion.div>
    </div>
  )
}

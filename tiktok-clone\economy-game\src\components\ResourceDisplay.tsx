'use client'

import { useGameStore } from '@/lib/gameStore'

export default function ResourceDisplay() {
  const { resources } = useGameStore()

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toLocaleString()
  }

  return (
    <div className="p-4">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {/* Money */}
        <div className="bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform">
          <div className="flex items-center space-x-2">
            <div className="bg-green-500 p-2 rounded-lg">
              <span className="text-white text-sm">💰</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs text-gray-300">المال</p>
              <p className="font-bold text-lg text-green-400 truncate">
                ${formatNumber(resources.money)}
              </p>
            </div>
          </div>
        </div>

        {/* Gems */}
        <div className="bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform">
          <div className="flex items-center space-x-2">
            <div className="bg-purple-500 p-2 rounded-lg">
              <span className="text-white text-sm">💎</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs text-gray-300">الجواهر</p>
              <p className="font-bold text-lg text-purple-400 truncate">
                {formatNumber(resources.gems)}
              </p>
            </div>
          </div>
        </div>

        {/* Energy */}
        <div className="bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform">
          <div className="flex items-center space-x-2">
            <div className="bg-blue-500 p-2 rounded-lg">
              <span className="text-white text-sm">⚡</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs text-gray-300">الطاقة</p>
              <p className="font-bold text-lg text-blue-400 truncate">
                {resources.energy}/100
              </p>
            </div>
          </div>
        </div>

        {/* Level */}
        <div className="bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 hover:scale-105 transition-transform">
          <div className="flex items-center space-x-2">
            <div className="bg-yellow-500 p-2 rounded-lg">
              <span className="text-white text-sm">⭐</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs text-gray-300">المستوى</p>
              <p className="font-bold text-lg text-yellow-400 truncate">
                {resources.level}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
